'use client';

import { useState, useEffect } from 'react';

interface TopDownDemoProps {
  className?: string;
  autoPlay?: boolean;
}

export default function TopDownDemo({ 
  className = '', 
  autoPlay = true 
}: TopDownDemoProps) {
  const [isActive, setIsActive] = useState(autoPlay);
  const [trainProgress, setTrainProgress] = useState(0);

  useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        setTrainProgress(prev => (prev + 0.02) % 1);
      }, 50);
      return () => clearInterval(interval);
    }
  }, [isActive]);

  // Calculate horizontal train position with smooth continuous loop
  // Extended range allows train to move past visible area before re-entering
  const extendedProgress = trainProgress * 1.4; // Extend range by 40% for smooth transition
  const trainX = -400 + (1120 * extendedProgress); // Extended horizontal movement range
  const trainY = 0; // Fixed Y position - no vertical movement
  const trainScale = 1.0; // Consistent size throughout movement (no scaling)
  const trainRotation = 0; // Horizontal orientation for top-down perspective

  // Calculate visible position and visibility for smooth wrapping
  const getTrainDisplay = () => {
    if (extendedProgress <= 1.1) {
      // Extended movement: train moves from left (10%) past the right border (110%)
      // This allows the train to completely exit the visible area before wrapping
      return {
        position: 10 + (100 * extendedProgress), // 10% to 110% (goes off-screen)
        visible: true
      };
    } else {
      // Train is in wrap-around phase - hide it completely
      // It will reappear at the start of the next cycle
      return {
        position: 10, // Reset to start position for next cycle
        visible: false
      };
    }
  };

  const trainDisplay = getTrainDisplay();

  // Calculate turbine activity based on train proximity
  const getTurbineActivity = (turbineIndex: number) => {
    const turbineX = -250 + (turbineIndex * 125);
    const distance = Math.abs(trainX - turbineX);
    // Only activate turbines when train is in visible area (not during wrap-around)
    const isTrainVisible = extendedProgress <= 1.1;
    return distance < 100 && isTrainVisible ? 1 : 0;
  };

  return (
    <div className={`relative bg-slate-900 rounded-xl overflow-hidden ${className}`}>
      {/* Top-down tunnel view */}
      <div className="relative aspect-video bg-gradient-to-br from-slate-800 to-slate-900 p-4">
        
        {/* Tunnel walls with perspective */}
        <div className="absolute inset-0">
          {/* Left tunnel wall */}
          <div className="absolute left-4 top-1/4 bottom-1/4 w-2 bg-gradient-to-b from-slate-600 to-slate-700 rounded-full transform -skew-y-12"></div>
          
          {/* Right tunnel wall */}
          <div className="absolute right-4 top-1/4 bottom-1/4 w-2 bg-gradient-to-b from-slate-600 to-slate-700 rounded-full transform skew-y-12"></div>
          
          {/* Tunnel depth lines */}
          {[...Array(10)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-slate-600 opacity-30"
              style={{
                left: `${20 + i * 8}%`,
                top: `${30 + i * 4}%`,
                width: '1px',
                height: `${40 - i * 2}%`,
                transform: `rotate(${-10 + i * 2}deg)`
              }}
            />
          ))}
        </div>

        {/* Savonius Turbines */}
        <div className="absolute inset-0">
          {[...Array(5)].map((_, i) => {
            const isActive = getTurbineActivity(i);
            const turbineX = 15 + i * 17.5;
            const turbineY = 35 + (i % 2) * 30;
            
            return (
              <div
                key={i}
                className="absolute"
                style={{
                  left: `${turbineX}%`,
                  top: `${turbineY}%`,
                  transform: 'translate(-50%, -50%)'
                }}
              >
                {/* Turbine housing */}
                <div className="relative w-6 h-8 bg-slate-400 rounded-lg">
                  {/* Cyan Savonius S-shaped blade based on reference image */}
                  <div
                    className={`absolute inset-0 ${isActive ? 'animate-spin' : ''}`}
                    style={{ animationDuration: '0.5s' }}
                  >
                    {/* Single cyan S-shaped blade - exact reference match */}
                    <div className="absolute inset-0">
                      {/* Top semicircle of S */}
                      <div className="absolute top-0 left-0 w-3 h-4 border-2 border-cyan-500 border-b-0 rounded-t-full bg-gradient-to-b from-cyan-400 to-transparent opacity-90"></div>
                      {/* Bottom semicircle of S (offset) */}
                      <div className="absolute bottom-0 right-0 w-3 h-4 border-2 border-cyan-500 border-t-0 rounded-b-full bg-gradient-to-t from-cyan-400 to-transparent opacity-90"></div>
                    </div>

                    {/* Central hub */}
                    <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-slate-400 opacity-40 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
                  </div>
                  
                  {/* Center hub */}
                  <div className="absolute top-1/2 left-1/2 w-1 h-1 bg-slate-700 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
                  
                  {/* Energy indicator */}
                  <div className={`absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full ${
                    isActive ? 'bg-emerald-400 animate-pulse' : 'bg-slate-500'
                  }`}></div>
                  
                  {/* Power display */}
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 text-xs text-center">
                    <div className={`text-xs ${isActive ? 'text-emerald-400' : 'text-slate-500'}`}>
                      {isActive ? '12kW' : '0kW'}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Enhanced High-Speed Train - Top-Down Perspective */}
        <div className="absolute inset-0 overflow-hidden">
          {trainDisplay.visible && (
            <div
              className="absolute transition-all duration-75 ease-linear"
              style={{
                left: `${trainDisplay.position}%`,
                top: `50%`,
                transform: `translate(-50%, -50%) scale(${trainScale}) rotate(${trainRotation}deg)`,
                zIndex: 10
              }}
            >
            {/* Enhanced Motion Blur Effects for High-Speed Horizontal Movement */}
            {isActive && (
              <>
                {/* Primary horizontal motion blur */}
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-300 to-transparent opacity-40 blur-md transform scale-150"
                  style={{ transform: `scale(150%) translateX(-${trainProgress * 30}px)` }}
                ></div>
                {/* Secondary speed blur with cyan accent */}
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-25 blur-lg transform scale-200"
                  style={{ transform: `scale(200%) translateX(-${trainProgress * 40}px)` }}
                ></div>
              </>
            )}

            {/* Modern High-Speed Train Body - Optimized for Top-Down View */}
            <div className="relative">
              {/* Main train body - Shinkansen/Bullet Train style from above */}
              <div className="w-20 h-6 bg-gradient-to-r from-slate-100 via-slate-200 to-slate-300 rounded-2xl shadow-2xl relative border border-slate-300">

                {/* Cyan accent stripes - clearly visible from top-down perspective */}
                <div className="absolute top-1 left-1 right-3 h-1 bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 rounded-full"></div>
                <div className="absolute bottom-1 left-1 right-3 h-0.5 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full opacity-80"></div>

                {/* Central body stripe for depth */}
                <div className="absolute top-1/2 left-1 right-3 h-0.5 bg-gradient-to-r from-slate-300 to-slate-400 rounded-full opacity-60 transform -translate-y-1/2"></div>

                {/* Train cars separation lines - visible from above */}
                <div className="absolute top-0 bottom-0 left-5 w-0.5 bg-slate-400 opacity-40"></div>
                <div className="absolute top-0 bottom-0 left-10 w-0.5 bg-slate-400 opacity-40"></div>
                <div className="absolute top-0 bottom-0 left-15 w-0.5 bg-slate-400 opacity-40"></div>

                {/* Aerodynamic nose - bullet train characteristic from top-down */}
                <div className="absolute -right-3 top-0 w-6 h-6 bg-gradient-to-br from-slate-300 via-slate-400 to-slate-500 rounded-r-full shadow-xl border-r border-slate-400">
                  {/* Nose tip highlight */}
                  <div className="absolute top-0.5 right-0.5 w-2 h-5 bg-gradient-to-br from-slate-200 to-transparent rounded-r-full opacity-60"></div>
                  {/* Front lights visible from above */}
                  <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-400 rounded-full opacity-90"></div>
                  <div className="absolute bottom-1 right-1 w-1 h-1 bg-red-500 rounded-full opacity-90"></div>
                </div>

                {/* Roof equipment - pantograph and AC units visible from top-down */}
                <div className="absolute top-0 left-3 w-1 h-1 bg-slate-600 rounded opacity-80"></div>
                <div className="absolute top-0 left-7 w-2 h-1 bg-slate-500 rounded opacity-70"></div>
                <div className="absolute top-0 left-12 w-1 h-1 bg-slate-600 rounded opacity-80"></div>

                {/* Window sections visible from above */}
                <div className="absolute top-0.5 left-2 w-2 h-2 bg-gradient-to-br from-slate-100 to-slate-200 rounded opacity-70"></div>
                <div className="absolute top-0.5 left-6 w-2 h-2 bg-gradient-to-br from-slate-100 to-slate-200 rounded opacity-70"></div>
                <div className="absolute top-0.5 left-10 w-2 h-2 bg-gradient-to-br from-slate-100 to-slate-200 rounded opacity-70"></div>
                <div className="absolute top-0.5 left-14 w-2 h-2 bg-gradient-to-br from-slate-100 to-slate-200 rounded opacity-70"></div>
              </div>

              {/* Enhanced aerodynamic effects and wind displacement for horizontal movement */}
              {isActive && (
                <>
                  {/* Leading wind displacement */}
                  <div className="absolute -left-8 -top-2 w-8 h-10">
                    {[...Array(4)].map((_, i) => (
                      <div
                        key={`lead-wind-${i}`}
                        className="absolute w-6 h-0.5 bg-cyan-400 opacity-60 animate-pulse"
                        style={{
                          top: `${20 + i * 15}%`,
                          left: `${i * 2}px`,
                          animationDelay: `${i * 0.1}s`,
                          animationDuration: '0.6s'
                        }}
                      ></div>
                    ))}
                  </div>

                  {/* Trailing wind effects */}
                  <div className="absolute -right-12 -top-2 w-12 h-10">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={`trail-wind-${i}`}
                        className="absolute w-8 h-0.5 bg-gradient-to-r from-cyan-300 to-transparent opacity-50 animate-pulse"
                        style={{
                          top: `${20 + i * 12}%`,
                          right: `${i * 3}px`,
                          animationDelay: `${i * 0.15}s`,
                          animationDuration: '0.8s'
                        }}
                      ></div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
          )}
        </div>

        {/* Enhanced Wind Flow Visualization - Coordinated with Horizontal Train Movement */}
        {isActive && extendedProgress <= 1.1 && (
          <div className="absolute inset-0">
            {[...Array(12)].map((_, i) => {
              // Calculate wind flow positions following horizontal train path
              const windX = trainX + 100 + (i * 30);
              const windY = (i % 3) * 50 - 25; // Spread vertically around center
              const windOpacity = Math.max(0.3, 0.8 - (i * 0.05));

              return (
                <div
                  key={`wind-flow-${i}`}
                  className="absolute animate-pulse"
                  style={{
                    left: `${15 + (windX / 10)}%`,
                    top: `${50 + (windY / 8)}%`,
                    animationDelay: `${i * 0.15}s`,
                    animationDuration: '1.2s',
                    opacity: windOpacity
                  }}
                >
                  <div
                    className="w-6 h-0.5 bg-gradient-to-r from-cyan-400 to-cyan-500"
                    style={{
                      boxShadow: '0 0 4px rgba(6, 182, 212, 0.5)'
                    }}
                  ></div>
                </div>
              );
            })}

            {/* Additional turbulence effects near train */}
            {[...Array(6)].map((_, i) => {
              const turbulenceX = trainX - 50 + (i * 15);
              const turbulenceY = (i % 2) * 40 - 20; // Vertical spread around center

              return (
                <div
                  key={`turbulence-${i}`}
                  className="absolute animate-ping"
                  style={{
                    left: `${15 + (turbulenceX / 10)}%`,
                    top: `${50 + (turbulenceY / 8)}%`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: '0.8s'
                  }}
                >
                  <div className="w-2 h-2 bg-cyan-300 rounded-full opacity-40"></div>
                </div>
              );
            })}
          </div>
        )}

        {/* Enhanced Control Overlay */}
        <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm">
          <div className="font-semibold mb-2 text-cyan-400">Top-Down View Demo</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-slate-300">Train Speed:</span>
              <span className="text-cyan-400">{Math.round(trainProgress * 100)} km/h</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Loop Progress:</span>
              <span className="text-emerald-400">{Math.round(trainProgress * 100)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Train Position:</span>
              <span className="text-slate-400">
                {extendedProgress > 1.1 ? 'Transitioning' :
                 extendedProgress > 1.0 ? 'Exiting Right' :
                 extendedProgress < 0.3 ? 'Left Side' :
                 extendedProgress > 0.7 ? 'Right Side' : 'Center'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Active Turbines:</span>
              <span className="text-emerald-400">
                {[...Array(5)].reduce((count, _, i) => count + getTurbineActivity(i), 0)}
              </span>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="absolute top-4 right-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-xs">
          <div className="font-semibold mb-2">Legend</div>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-slate-300 rounded"></div>
              <span>High-Speed Train</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-cyan-400 rounded"></div>
              <span>Wind Flow</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-emerald-400 rounded animate-pulse"></div>
              <span>Active Turbine</span>
            </div>
          </div>
        </div>

        {/* Animation control */}
        <div className="absolute bottom-4 right-4">
          <button
            onClick={() => setIsActive(!isActive)}
            className="bg-slate-800 bg-opacity-90 text-white px-4 py-2 rounded-lg font-semibold hover:bg-opacity-100 transition-all duration-200"
          >
            {isActive ? 'Pause' : 'Play'} Demo
          </button>
        </div>

        {/* Professional branding */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-slate-800 bg-opacity-90 rounded-lg px-4 py-2 text-white text-center">
            <div className="font-semibold text-sm">Tunnel Wind Capture</div>
            <div className="text-xs text-slate-300">Top-Down Perspective Demonstration</div>
          </div>
        </div>
      </div>

      {/* Technical summary */}
      <div className="bg-slate-800 p-4">
        <div className="grid grid-cols-3 gap-4 text-center text-sm">
          <div>
            <div className="text-cyan-400 font-semibold">Diagonal Path</div>
            <div className="text-slate-300">Upper-left to lower-right</div>
          </div>
          <div>
            <div className="text-emerald-400 font-semibold">Scale Effect</div>
            <div className="text-slate-300">0.4x to 1.2x progression</div>
          </div>
          <div>
            <div className="text-purple-400 font-semibold">Perspective</div>
            <div className="text-slate-300">Realistic depth simulation</div>
          </div>
        </div>
      </div>
    </div>
  );
}
