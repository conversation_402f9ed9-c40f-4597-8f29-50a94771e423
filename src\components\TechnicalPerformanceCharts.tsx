'use client';

import React, { useState } from 'react';
import { Line, Bar, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
} from 'chart.js';

ChartJS.register(RadialLinearScale);

interface TechnicalPerformanceChartsProps {
  className?: string;
}

const TechnicalPerformanceCharts: React.FC<TechnicalPerformanceChartsProps> = ({ className = '' }) => {
  const [timeRange, setTimeRange] = useState<'daily' | 'monthly' | 'yearly'>('monthly');

  // Historical performance data
  const generateHistoricalData = (range: string) => {
    const dataPoints = range === 'daily' ? 24 : range === 'monthly' ? 30 : 12;
    const labels = range === 'daily' 
      ? Array.from({ length: 24 }, (_, i) => `${i}:00`)
      : range === 'monthly'
      ? Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`)
      : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const baseValue = range === 'daily' ? 15 : range === 'monthly' ? 350 : 4200;
    const variation = range === 'daily' ? 10 : range === 'monthly' ? 100 : 800;

    return {
      labels,
      datasets: [
        {
          label: `Energy Generation (${range === 'daily' ? 'kWh' : range === 'monthly' ? 'kWh' : 'MWh'})`,
          data: Array.from({ length: dataPoints }, () => 
            baseValue + (Math.random() - 0.5) * variation
          ),
          borderColor: '#06b6d4',
          backgroundColor: 'rgba(6, 182, 212, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Target Performance',
          data: Array.from({ length: dataPoints }, () => baseValue),
          borderColor: '#10b981',
          borderWidth: 2,
          borderDash: [5, 5],
          fill: false,
        },
      ],
    };
  };

  // Wind condition efficiency data
  const windEfficiencyData = {
    labels: ['3-5 m/s', '5-8 m/s', '8-12 m/s', '12-15 m/s', '15-20 m/s', '20+ m/s'],
    datasets: [
      {
        label: 'Efficiency (%)',
        data: [25, 45, 85, 95, 88, 75],
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(6, 182, 212, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)',
        ],
        borderColor: [
          '#dc2626',
          '#d97706',
          '#059669',
          '#0891b2',
          '#7c3aed',
          '#be185d',
        ],
        borderWidth: 2,
      },
    ],
  };

  // Comparative analysis data
  const comparisonData = {
    labels: [
      'Capacity Factor',
      'Efficiency',
      'Reliability',
      'Maintenance',
      'Environmental Impact',
      'Cost Effectiveness'
    ],
    datasets: [
      {
        label: 'Tunnel Wind Capture',
        data: [85, 87, 95, 90, 98, 92],
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.2)',
        borderWidth: 3,
      },
      {
        label: 'Traditional Wind',
        data: [35, 45, 85, 70, 85, 75],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.2)',
        borderWidth: 2,
      },
      {
        label: 'Solar PV',
        data: [25, 22, 90, 85, 90, 80],
        borderColor: '#f59e0b',
        backgroundColor: 'rgba(245, 158, 11, 0.2)',
        borderWidth: 2,
      },
    ],
  };

  // System reliability data
  const reliabilityData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Uptime (%)',
        data: [99.2, 99.5, 99.1, 99.8, 99.3, 99.6, 99.4, 99.7, 99.2, 99.5, 99.3, 99.1],
        backgroundColor: '#10b981',
        borderColor: '#059669',
        borderWidth: 2,
      },
      {
        label: 'Target Uptime (%)',
        data: Array(12).fill(99.0),
        backgroundColor: '#64748b',
        borderColor: '#475569',
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#e2e8f0',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
      y: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
    },
  };

  const radarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#e2e8f0',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
        },
      },
    },
    scales: {
      r: {
        angleLines: {
          color: '#334155',
        },
        grid: {
          color: '#334155',
        },
        pointLabels: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        ticks: {
          color: '#94a3b8',
          backdropColor: 'transparent',
        },
        min: 0,
        max: 100,
      },
    },
  };

  return (
    <div className={`bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-industrial ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-2xl font-bold text-white">Technical Performance Analytics</h3>
        
        {/* Time Range Selector */}
        <div className="flex bg-slate-700 rounded-lg p-1">
          {(['daily', 'monthly', 'yearly'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 ${
                timeRange === range
                  ? 'bg-cyan-500 text-white'
                  : 'text-slate-300 hover:text-white'
              }`}
            >
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Historical Performance */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">
            Historical Performance ({timeRange})
          </h4>
          <div className="h-64">
            <Line data={generateHistoricalData(timeRange)} options={chartOptions} />
          </div>
        </div>

        {/* Wind Condition Efficiency */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">Efficiency by Wind Speed</h4>
          <div className="h-64">
            <Bar data={windEfficiencyData} options={chartOptions} />
          </div>
        </div>

        {/* Comparative Analysis */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">Technology Comparison</h4>
          <div className="h-64">
            <Radar data={comparisonData} options={radarOptions} />
          </div>
        </div>

        {/* System Reliability */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">System Reliability</h4>
          <div className="h-64">
            <Bar data={reliabilityData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-cyan-600 to-cyan-700 rounded-lg p-4 text-white">
          <div className="text-cyan-100 text-sm font-semibold">Average Efficiency</div>
          <div className="text-2xl font-bold">87.3%</div>
          <div className="text-xs text-cyan-200">+2.1% vs last month</div>
        </div>
        
        <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-lg p-4 text-white">
          <div className="text-emerald-100 text-sm font-semibold">Capacity Factor</div>
          <div className="text-2xl font-bold">85.2%</div>
          <div className="text-xs text-emerald-200">Industry leading</div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-4 text-white">
          <div className="text-blue-100 text-sm font-semibold">System Uptime</div>
          <div className="text-2xl font-bold">99.4%</div>
          <div className="text-xs text-blue-200">Above target</div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-4 text-white">
          <div className="text-purple-100 text-sm font-semibold">Performance Index</div>
          <div className="text-2xl font-bold">94.7</div>
          <div className="text-xs text-purple-200">Excellent rating</div>
        </div>
      </div>
    </div>
  );
};

export default TechnicalPerformanceCharts;
