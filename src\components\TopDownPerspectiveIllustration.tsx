'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface TopDownPerspectiveIllustrationProps {
  className?: string;
  showOverlays?: boolean;
  interactive?: boolean;
  autoHighlight?: boolean;
}

export default function TopDownPerspectiveIllustration({ 
  className = '', 
  showOverlays = true,
  interactive = false,
  autoHighlight = true
}: TopDownPerspectiveIllustrationProps) {
  const [highlightedElement, setHighlightedElement] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [trainProgress, setTrainProgress] = useState(0);

  // Auto-highlight different elements and simulate train movement
  useEffect(() => {
    if (autoHighlight) {
      const elements = ['perspective', 'train-scaling', 'turbines-top', 'wind-diagonal'];
      let currentIndex = 0;

      const interval = setInterval(() => {
        setHighlightedElement(elements[currentIndex]);
        currentIndex = (currentIndex + 1) % elements.length;
        
        // Update train progress for demonstration
        setTrainProgress(prev => (prev + 0.02) % 1);
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [autoHighlight]);

  const handleElementClick = (element: string) => {
    if (interactive) {
      setHighlightedElement(element);
      setShowDetails(true);
    }
  };

  const getElementDetails = (element: string) => {
    const details = {
      'perspective': {
        title: "Top-Down Bird's Eye Perspective",
        description: "Unique bird's eye view looking down into the tunnel from above, showing the complete technology system from a perspective never seen before.",
        specs: ["90-degree top-down view", "Strong diagonal composition", "Depth perception from above", "Professional presentation angle"]
      },
      'train-scaling': {
        title: "Diagonal Movement with Perspective Scaling",
        description: "Train moves diagonally from upper-left background to lower-right foreground with realistic perspective scaling from 0.3x to 1.2x.",
        specs: ["Diagonal trajectory path", "0.3x to 1.2x scaling", "Realistic depth effect", "Dynamic movement visualization"]
      },
      'turbines-top': {
        title: "Savonius Turbines from Above",
        description: "Vertical-axis wind turbines viewed from the top, showing circular housings and rotation patterns clearly visible from bird's eye perspective.",
        specs: ["Circular turbine housings", "Top-down blade visibility", "Rotation motion indicators", "Strategic wall positioning"]
      },
      'wind-diagonal': {
        title: "Diagonal Wind Flow Patterns",
        description: "Aerodynamic wind visualization following the diagonal train path, showing how wind flows from the train into the turbines from above.",
        specs: ["Diagonal flow patterns", "Train-to-turbine convergence", "Top-down aerodynamics", "Cyan flow visualization"]
      }
    };
    return details[element as keyof typeof details];
  };

  // Calculate train position for demonstration
  const trainX = -300 + (600 * trainProgress);
  const trainY = -200 + (400 * trainProgress);
  const trainScale = 0.3 + (0.9 * trainProgress);

  return (
    <div className={`relative bg-slate-900 rounded-2xl overflow-hidden ${className}`}>
      {/* Main Illustration Container */}
      <div className="relative aspect-video bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900">
        
        {/* Placeholder for Professional Top-Down Illustration */}
        <div className="absolute inset-0 flex items-center justify-center">
          {/* This will be replaced with the actual top-down perspective illustration */}
          <div className="text-center text-white p-8">
            <div className="w-32 h-32 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg className="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold mb-4">Top-Down Perspective View</h3>
            <p className="text-slate-300 max-w-md mx-auto mb-4">
              Professional bird&apos;s eye view illustration showing diagonal train movement with perspective scaling
            </p>
            <div className="text-sm text-slate-400">
              Train trajectory: Upper-left background to lower-right foreground
            </div>
          </div>
        </div>

        {/* Interactive Hotspots for Top-Down Elements */}
        {interactive && (
          <>
            {/* Perspective Hotspot */}
            <button
              className={`absolute top-4 left-4 w-8 h-8 rounded-full border-2 border-white transition-all duration-300 ${
                highlightedElement === 'perspective' 
                  ? 'bg-purple-500 bg-opacity-80 scale-110 animate-pulse' 
                  : 'bg-purple-500 bg-opacity-60 hover:scale-110'
              }`}
              onClick={() => handleElementClick('perspective')}
              aria-label="Perspective Details"
            />

            {/* Train Scaling Hotspot */}
            <button
              className={`absolute top-1/3 left-1/2 w-8 h-8 rounded-full border-2 border-white transition-all duration-300 ${
                highlightedElement === 'train-scaling' 
                  ? 'bg-cyan-500 bg-opacity-80 scale-110 animate-pulse' 
                  : 'bg-cyan-500 bg-opacity-60 hover:scale-110'
              }`}
              onClick={() => handleElementClick('train-scaling')}
              aria-label="Train Scaling Details"
            />

            {/* Turbines Top View Hotspots */}
            <button
              className={`absolute top-1/4 left-1/4 w-6 h-6 rounded-full border-2 border-white transition-all duration-300 ${
                highlightedElement === 'turbines-top' 
                  ? 'bg-emerald-500 bg-opacity-80 scale-110 animate-pulse' 
                  : 'bg-emerald-500 bg-opacity-60 hover:scale-110'
              }`}
              onClick={() => handleElementClick('turbines-top')}
              aria-label="Top-Down Turbines"
            />
            <button
              className={`absolute bottom-1/4 right-1/4 w-6 h-6 rounded-full border-2 border-white transition-all duration-300 ${
                highlightedElement === 'turbines-top' 
                  ? 'bg-emerald-500 bg-opacity-80 scale-110 animate-pulse' 
                  : 'bg-emerald-500 bg-opacity-60 hover:scale-110'
              }`}
              onClick={() => handleElementClick('turbines-top')}
              aria-label="Top-Down Turbines"
            />

            {/* Wind Flow Diagonal Hotspot */}
            <button
              className={`absolute top-1/2 left-2/3 w-6 h-6 rounded-full border-2 border-white transition-all duration-300 ${
                highlightedElement === 'wind-diagonal' 
                  ? 'bg-blue-500 bg-opacity-80 scale-110 animate-pulse' 
                  : 'bg-blue-500 bg-opacity-60 hover:scale-110'
              }`}
              onClick={() => handleElementClick('wind-diagonal')}
              aria-label="Diagonal Wind Flow"
            />
          </>
        )}

        {/* Professional Overlays for Top-Down View */}
        {showOverlays && (
          <>
            {/* Top-Down Specifications */}
            <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-4 text-white max-w-xs">
              <h4 className="font-bold text-purple-400 mb-3">Top-Down Perspective</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-300">View Angle:</span>
                  <span>Bird&apos;s Eye (90°)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Train Path:</span>
                  <span>Diagonal</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Scale Range:</span>
                  <span className="text-cyan-400">0.3x → 1.2x</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Perspective:</span>
                  <span>Strong Depth</span>
                </div>
              </div>
            </div>

            {/* Current Highlight Info */}
            {highlightedElement && (
              <div className="absolute top-4 right-4 bg-slate-800 bg-opacity-90 rounded-lg p-4 text-white max-w-xs">
                <div className="flex items-center space-x-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${
                    highlightedElement === 'perspective' ? 'bg-purple-400' :
                    highlightedElement === 'train-scaling' ? 'bg-cyan-400' :
                    highlightedElement === 'turbines-top' ? 'bg-emerald-400' :
                    'bg-blue-400'
                  } animate-pulse`}></div>
                  <span className="text-sm font-semibold">
                    {highlightedElement === 'perspective' ? 'Bird\'s Eye View' :
                     highlightedElement === 'train-scaling' ? 'Diagonal Scaling' :
                     highlightedElement === 'turbines-top' ? 'Top-Down Turbines' :
                     'Diagonal Wind Flow'}
                  </span>
                </div>
                <div className="text-xs text-slate-300">
                  {highlightedElement === 'perspective' ? 'Unique top-down perspective' :
                   highlightedElement === 'train-scaling' ? '0.3x to 1.2x scaling effect' :
                   highlightedElement === 'turbines-top' ? 'Circular housings from above' :
                   'Diagonal aerodynamic patterns'}
                </div>
              </div>
            )}

            {/* Train Progress Indicator */}
            <div className="absolute bottom-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-4 text-white">
              <div className="text-sm font-semibold mb-2">Train Movement Demo</div>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-slate-300">Position:</span>
                  <span>{Math.round(trainProgress * 100)}%</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-slate-300">Scale:</span>
                  <span className="text-cyan-400">{trainScale.toFixed(1)}x</span>
                </div>
                <div className="w-32 h-2 bg-slate-600 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-cyan-400 to-emerald-400 transition-all duration-100"
                    style={{ width: `${trainProgress * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Professional Branding */}
        <div className="absolute bottom-4 right-4 bg-slate-800 bg-opacity-90 rounded-lg px-4 py-2 text-white">
          <div className="text-sm font-semibold">Tunnel Wind Capture Technology</div>
          <div className="text-xs text-slate-300">Top-Down Bird&apos;s Eye Perspective</div>
        </div>
      </div>

      {/* Detail Panel for Top-Down Elements */}
      {showDetails && highlightedElement && (
        <div className="absolute inset-0 bg-slate-900 bg-opacity-95 flex items-center justify-center z-10">
          <div className="bg-white rounded-2xl p-8 max-w-lg mx-4">
            <div className="flex justify-between items-start mb-6">
              <h3 className="text-2xl font-bold text-slate-800">
                {getElementDetails(highlightedElement)?.title}
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-slate-500 hover:text-slate-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <p className="text-slate-600 mb-6">
              {getElementDetails(highlightedElement)?.description}
            </p>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-slate-800">Key Features:</h4>
              <ul className="space-y-2">
                {getElementDetails(highlightedElement)?.specs.map((spec, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-600 text-sm">{spec}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Technical Information Footer - Top-Down Specific */}
      <div className="bg-slate-800 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center text-sm">
          <div>
            <div className="text-purple-400 font-semibold mb-1">Bird&apos;s Eye View</div>
            <div className="text-slate-300">Top-down perspective</div>
          </div>
          <div>
            <div className="text-cyan-400 font-semibold mb-1">Diagonal Movement</div>
            <div className="text-slate-300">Upper-left to lower-right</div>
          </div>
          <div>
            <div className="text-emerald-400 font-semibold mb-1">Perspective Scaling</div>
            <div className="text-slate-300">0.3x to 1.2x progression</div>
          </div>
          <div>
            <div className="text-blue-400 font-semibold mb-1">Depth Effect</div>
            <div className="text-slate-300">Strong perspective depth</div>
          </div>
        </div>
      </div>
    </div>
  );
}
