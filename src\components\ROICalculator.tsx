'use client';

import React, { useState, useEffect } from 'react';
import { Line, Bar } from 'react-chartjs-2';

interface ROICalculatorProps {
  className?: string;
}

interface CalculationInputs {
  tunnelLength: number;
  trainFrequency: number;
  energyCost: number;
  initialInvestment: number;
  maintenanceCost: number;
}

const ROICalculator: React.FC<ROICalculatorProps> = ({ className = '' }) => {
  const [inputs, setInputs] = useState<CalculationInputs>({
    tunnelLength: 5, // km
    trainFrequency: 50, // trains per day
    energyCost: 0.12, // $ per kWh
    initialInvestment: 2500000, // $2.5M
    maintenanceCost: 50000, // $50k per year
  });

  const [results, setResults] = useState({
    annualEnergy: 0,
    annualRevenue: 0,
    paybackPeriod: 0,
    roi: 0,
    netPresentValue: 0,
  });

  // Calculate ROI metrics
  useEffect(() => {
    // Energy generation calculation
    const turbinesPerKm = 8;
    const totalTurbines = inputs.tunnelLength * turbinesPerKm;
    const energyPerTrain = 0.5; // kWh per train passage per turbine
    const annualEnergy = totalTurbines * inputs.trainFrequency * energyPerTrain * 365;
    
    // Financial calculations
    const annualRevenue = annualEnergy * inputs.energyCost;
    const netAnnualRevenue = annualRevenue - inputs.maintenanceCost;
    const paybackPeriod = inputs.initialInvestment / netAnnualRevenue;
    const roi = ((netAnnualRevenue * 10 - inputs.initialInvestment) / inputs.initialInvestment) * 100;
    
    // NPV calculation (10 years, 8% discount rate)
    const discountRate = 0.08;
    let npv = -inputs.initialInvestment;
    for (let year = 1; year <= 10; year++) {
      npv += netAnnualRevenue / Math.pow(1 + discountRate, year);
    }

    setResults({
      annualEnergy,
      annualRevenue,
      paybackPeriod,
      roi,
      netPresentValue: npv,
    });
  }, [inputs]);

  // Chart data for payback analysis
  const paybackChartData = {
    labels: Array.from({ length: 11 }, (_, i) => `Year ${i}`),
    datasets: [
      {
        label: 'Cumulative Cash Flow ($)',
        data: Array.from({ length: 11 }, (_, year) => {
          if (year === 0) return -inputs.initialInvestment;
          const netAnnualRevenue = results.annualRevenue - inputs.maintenanceCost;
          return -inputs.initialInvestment + (netAnnualRevenue * year);
        }),
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
      },
      {
        label: 'Break-even Line',
        data: Array.from({ length: 11 }, () => 0),
        borderColor: '#ef4444',
        borderWidth: 2,
        borderDash: [5, 5],
        fill: false,
      },
    ],
  };

  // Revenue comparison chart
  const comparisonData = {
    labels: ['Traditional Grid', 'Solar Farm', 'Wind Farm', 'Tunnel Wind Capture'],
    datasets: [
      {
        label: 'Levelized Cost ($/MWh)',
        data: [85, 45, 35, 28],
        backgroundColor: ['#64748b', '#f59e0b', '#10b981', '#06b6d4'],
        borderColor: ['#475569', '#d97706', '#059669', '#0891b2'],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#e2e8f0',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
      y: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
    },
  };

  const handleInputChange = (field: keyof CalculationInputs, value: number) => {
    setInputs(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className={`dashboard-card p-6 lg:p-8 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-2xl lg:text-3xl font-bold text-white">ROI Calculator & Business Case</h3>
            <p className="text-slate-300 text-sm lg:text-base">Calculate the financial returns of tunnel wind capture technology</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 lg:gap-12">
        {/* Input Parameters */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-cyan-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h4 className="text-lg lg:text-xl font-semibold text-white">Project Parameters</h4>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Tunnel Length (km)
              </label>
              <input
                type="number"
                value={inputs.tunnelLength}
                onChange={(e) => handleInputChange('tunnelLength', parseFloat(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                min="1"
                max="50"
                step="0.5"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Train Frequency (trains/day)
              </label>
              <input
                type="number"
                value={inputs.trainFrequency}
                onChange={(e) => handleInputChange('trainFrequency', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                min="10"
                max="500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Energy Cost ($/kWh)
              </label>
              <input
                type="number"
                value={inputs.energyCost}
                onChange={(e) => handleInputChange('energyCost', parseFloat(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                min="0.05"
                max="0.50"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Initial Investment ($)
              </label>
              <input
                type="number"
                value={inputs.initialInvestment}
                onChange={(e) => handleInputChange('initialInvestment', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                min="500000"
                max="10000000"
                step="100000"
              />
            </div>
          </div>

          {/* Key Results */}
          <div className="mt-6 space-y-3">
            <h4 className="text-lg font-semibold text-white">Financial Results</h4>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
                <div className="text-cyan-400 text-xs font-semibold">Annual Energy</div>
                <div className="text-lg font-bold text-white">
                  {results.annualEnergy.toLocaleString()} kWh
                </div>
              </div>
              
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
                <div className="text-emerald-400 text-xs font-semibold">Annual Revenue</div>
                <div className="text-lg font-bold text-white">
                  ${results.annualRevenue.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
                <div className="text-blue-400 text-xs font-semibold">Payback Period</div>
                <div className="text-lg font-bold text-white">
                  {results.paybackPeriod.toFixed(1)} years
                </div>
              </div>
              
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
                <div className="text-purple-400 text-xs font-semibold">10-Year ROI</div>
                <div className="text-lg font-bold text-white">
                  {results.roi.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="space-y-6">
          {/* Payback Analysis Chart */}
          <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
            <h4 className="text-lg font-semibold text-white mb-4">Payback Analysis</h4>
            <div className="h-64">
              <Line data={paybackChartData} options={chartOptions} />
            </div>
          </div>

          {/* Cost Comparison Chart */}
          <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
            <h4 className="text-lg font-semibold text-white mb-4">Cost Comparison</h4>
            <div className="h-64">
              <Bar data={comparisonData} options={chartOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Download Business Case Button */}
      <div className="mt-6 text-center">
        <button className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
          Download Business Case Report
        </button>
      </div>
    </div>
  );
};

export default ROICalculator;
