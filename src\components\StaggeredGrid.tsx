'use client';

import { useEffect, useRef, useState } from 'react';

interface StaggeredGridProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  animation?: 'fade-up' | 'fade-left' | 'fade-right' | 'zoom-in' | 'bounce-in';
  className?: string;
  threshold?: number;
}

export default function StaggeredGrid({
  children,
  staggerDelay = 100,
  animation = 'fade-up',
  className = '',
  threshold = 0.1
}: StaggeredGridProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        threshold,
        rootMargin: '50px'
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold]);

  const getAnimationClass = () => {
    if (!isVisible) return 'opacity-0 translate-y-8';
    
    switch (animation) {
      case 'fade-up':
        return 'animate-slide-up-fade';
      case 'fade-left':
        return 'animate-slide-left-fade';
      case 'fade-right':
        return 'animate-slide-right-fade';
      case 'zoom-in':
        return 'animate-zoom-in';
      case 'bounce-in':
        return 'animate-bounce-in';
      default:
        return 'animate-slide-up-fade';
    }
  };

  return (
    <div ref={ref} className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={`transition-all duration-1000 ${getAnimationClass()}`}
          style={{ 
            animationDelay: isVisible ? `${index * staggerDelay}ms` : '0ms',
            animationFillMode: 'both'
          }}
        >
          {child}
        </div>
      ))}
    </div>
  );
}
