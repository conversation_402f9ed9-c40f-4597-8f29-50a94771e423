@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Professional Rail & Tunnel Color Palette */
  --steel-gray: #4a5568;
  --steel-gray-light: #718096;
  --steel-gray-dark: #2d3748;
  --tunnel-dark: #1a202c;
  --tunnel-medium: #2d3748;
  --tunnel-light: #4a5568;
  --metallic-silver: #a0aec0;
  --metallic-light: #cbd5e0;
  --wind-blue: #3182ce;
  --wind-blue-light: #4299e1;
  --wind-cyan: #0891b2;
  --wind-cyan-light: #06b6d4;
  --eco-green: #059669;
  --eco-green-light: #10b981;
  --eco-green-muted: #047857;
  --industrial-orange: #ea580c;
  --industrial-orange-light: #fb923c;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for enhanced user experience */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth scrolling for better navigation */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
button:focus,
a:focus {
  outline: 2px solid #06b6d4;
  outline-offset: 2px;
}

/* Improved section spacing and visual hierarchy */
.section-spacing {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.section-spacing-large {
  padding-top: 7rem;
  padding-bottom: 7rem;
}

.section-spacing-small {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

/* Enhanced container max-widths for better readability */
.container-content {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-wide {
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Professional card styling */
.card-professional {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(148, 163, 184, 0.2);
  transition: all 0.3s ease;
}

.card-professional:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* Dashboard specific styling */
.dashboard-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 1rem;
  border: 1px solid #475569;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

.dashboard-metric {
  background: rgba(71, 85, 105, 0.8);
  border-radius: 0.75rem;
  border: 1px solid rgba(100, 116, 139, 0.3);
  backdrop-filter: blur(8px);
}

/* Professional tunnel/rail themed gradients */
.gradient-tunnel {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
}

.gradient-rail {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.gradient-wind {
  background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 50%, #10b981 100%);
}

.gradient-energy {
  background: linear-gradient(135deg, #f59e0b 0%, #eab308 50%, #84cc16 100%);
}

.gradient-light-professional {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Enhanced shadow utilities */
.shadow-soft {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-strong {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Responsive typography improvements */
@media (max-width: 640px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 2rem;
    line-height: 1.3;
  }

  .text-hero {
    font-size: 1.125rem;
    line-height: 1.6;
  }
}

/* Loading states and transitions */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Cool new animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-up-fade {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-left-fade {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-right-fade {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.8), 0 0 30px rgba(14, 165, 233, 0.6);
  }
}

@keyframes tunnel-wind {
  0% {
    transform: translateX(-100px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateX(0px) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(100px) scale(0.8);
    opacity: 0;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animation utility classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate 20s linear infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-slide-up-fade {
  animation: slide-up-fade 0.8s ease-out;
}

.animate-slide-left-fade {
  animation: slide-left-fade 0.8s ease-out;
}

.animate-slide-right-fade {
  animation: slide-right-fade 0.8s ease-out;
}

.animate-zoom-in {
  animation: zoom-in 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-tunnel-wind {
  animation: tunnel-wind 4s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Stagger animation delays */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-600 {
  animation-delay: 0.6s;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
}

/* Professional tunnel/rail themed effects */
.tunnel-depth {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
}

.rail-metallic {
  background: linear-gradient(145deg, #64748b, #475569);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2), -2px -2px 5px rgba(255, 255, 255, 0.1);
}

/* Additional professional hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(45, 55, 72, 0.15);
}

.hover-glow-green {
  transition: box-shadow 0.3s ease;
}

.hover-glow-green:hover {
  box-shadow: 0 0 20px rgba(5, 150, 105, 0.4);
}

.hover-glow-steel {
  transition: box-shadow 0.3s ease;
}

.hover-glow-steel:hover {
  box-shadow: 0 0 20px rgba(160, 174, 192, 0.4);
}

/* Professional gradient backgrounds */
.gradient-tunnel {
  background: linear-gradient(135deg, var(--tunnel-dark) 0%, var(--tunnel-medium) 50%, var(--steel-gray) 100%);
}

.gradient-wind {
  background: linear-gradient(135deg, var(--wind-blue) 0%, var(--wind-cyan) 100%);
}

.gradient-rail {
  background: linear-gradient(135deg, var(--steel-gray-dark) 0%, var(--metallic-silver) 100%);
}

.gradient-eco-professional {
  background: linear-gradient(135deg, var(--eco-green-muted) 0%, var(--wind-cyan) 100%);
}

/* Industrial shadow utilities */
.shadow-industrial {
  box-shadow: 0 4px 6px -1px rgba(45, 55, 72, 0.1), 0 2px 4px -1px rgba(45, 55, 72, 0.06);
}

.shadow-tunnel {
  box-shadow: 0 10px 15px -3px rgba(26, 32, 44, 0.1), 0 4px 6px -2px rgba(26, 32, 44, 0.05);
}

.shadow-steel {
  box-shadow: 0 20px 25px -5px rgba(74, 85, 104, 0.1), 0 10px 10px -5px rgba(74, 85, 104, 0.04);
}

/* Tunnel Animation Keyframes */
@keyframes train-approach {
  0% {
    transform: translateY(-50%) translateX(-100px) scale(0.3);
    opacity: 0.3;
  }
  30% {
    transform: translateY(-50%) translateX(20%) scale(0.8);
    opacity: 0.8;
  }
  70% {
    transform: translateY(-50%) translateX(50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translateY(-50%) translateX(120%) scale(0.4);
    opacity: 0.2;
  }
}

@keyframes wind-flow {
  0% {
    opacity: 0;
    transform: translateX(-20px) scaleX(0.5);
  }
  50% {
    opacity: 1;
    transform: translateX(0px) scaleX(1);
  }
  100% {
    opacity: 0;
    transform: translateX(20px) scaleX(0.5);
  }
}

@keyframes turbine-spin-fast {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes energy-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes tunnel-depth {
  0% {
    transform: perspective(1000px) rotateX(0deg) scaleY(1);
  }
  100% {
    transform: perspective(1000px) rotateX(5deg) scaleY(0.8);
  }
}

/* Animation utility classes for tunnel demo */
.train-moving {
  animation: train-approach 8s ease-in-out infinite;
}

.train-static {
  transform: translateY(-50%) translateX(-20%) scale(0.3);
  opacity: 0.3;
}

.wind-flow-active {
  animation: wind-flow 2s ease-in-out infinite;
}

.turbine-active {
  animation: turbine-spin-fast 0.8s linear infinite;
}

.energy-active {
  animation: energy-pulse 1.5s ease-in-out infinite;
}

.tunnel-perspective {
  animation: tunnel-depth 8s ease-in-out infinite;
}

/* 3D perspective utilities */
.perspective-1000 {
  perspective: 1000px;
}

.rotateX-5 {
  transform: rotateX(5deg);
}

/* Enhanced wind effect */
@keyframes wind-particle {
  0% {
    opacity: 0;
    transform: translateX(-50px) translateY(0px);
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(100px) translateY(-10px);
  }
}

.wind-particle {
  animation: wind-particle 3s ease-out infinite;
}

/* Smooth transitions for all animation states */
.transition-8000 {
  transition-duration: 8000ms;
}
