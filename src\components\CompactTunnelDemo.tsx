'use client';

import { useState, useEffect } from 'react';

interface CompactTunnelDemoProps {
  className?: string;
  autoPlay?: boolean;
}

export default function CompactTunnelDemo({ 
  className = '', 
  autoPlay = true 
}: CompactTunnelDemoProps) {
  const [isActive, setIsActive] = useState(autoPlay);

  useEffect(() => {
    if (autoPlay) {
      const interval = setInterval(() => {
        setIsActive(prev => !prev);
        setTimeout(() => setIsActive(true), 100);
      }, 6000);

      return () => clearInterval(interval);
    }
  }, [autoPlay]);

  return (
    <div className={`relative overflow-hidden bg-gradient-to-r from-slate-800 to-slate-700 rounded-lg ${className}`}>
      {/* Compact Tunnel Structure */}
      <div className="relative h-24 flex items-center">
        {/* Tunnel walls */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-700 via-slate-800 to-slate-900 rounded-lg"></div>
        
        {/* Tunnel perspective lines */}
        <div className="absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-slate-600 opacity-30"
              style={{
                left: `${10 + i * 10}%`,
                top: '20%',
                width: '1px',
                height: '60%',
                transform: `scaleY(${0.4 + i * 0.075})`
              }}
            />
          ))}
        </div>

        {/* Compact Turbines */}
        <div className="absolute inset-0 flex items-center justify-around px-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="relative">
              {/* Turbine */}
              <div
                className={`w-4 h-6 relative ${isActive ? 'animate-spin' : ''}`}
                style={{
                  animationDuration: '0.6s',
                  animationDelay: `${i * 0.1}s`
                }}
              >
                {/* Compact cyan Savonius S-shaped blade - reference-accurate design */}
                <div className="absolute inset-0">
                  {/* Single cyan S-shaped blade - compact scale */}
                  <div className="absolute inset-0">
                    {/* Upper semicircle of S */}
                    <div className="absolute top-0 left-0 w-2 h-3 border border-cyan-500 border-b-0 rounded-t-full bg-gradient-to-b from-cyan-500 to-transparent opacity-90"></div>
                    {/* Lower semicircle of S (offset) */}
                    <div className="absolute bottom-0 right-0 w-2 h-3 border border-cyan-500 border-t-0 rounded-b-full bg-gradient-to-t from-cyan-500 to-transparent opacity-90"></div>
                  </div>

                  {/* Compact central hub */}
                  <div className="absolute top-1/2 left-1/2 w-1.5 h-1.5 bg-slate-500 opacity-50 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
                </div>
                <div className="absolute top-1/2 left-1/2 w-1 h-1 bg-slate-700 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
              </div>
              
              {/* Energy indicator */}
              <div className={`absolute -top-1 left-1/2 transform -translate-x-1/2 ${isActive ? 'animate-pulse' : ''}`}>
                <div className="w-0.5 h-0.5 bg-emerald-400 rounded-full"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Compact Train */}
        <div className="absolute inset-0 flex items-center">
          <div 
            className={`absolute top-1/2 transform -translate-y-1/2 transition-all duration-3000 ease-in-out ${
              isActive ? 'left-full' : '-left-8'
            }`}
            style={{
              transform: `translateY(-50%) scale(${isActive ? '1' : '0.6'})`,
            }}
          >
            {/* Compact train body */}
            <div className="w-16 h-4 bg-gradient-to-r from-slate-300 to-slate-400 rounded-r-lg shadow-md relative">
              {/* Train windows */}
              <div className="absolute top-0.5 left-1 w-3 h-1 bg-cyan-200 rounded-sm opacity-60"></div>
              <div className="absolute top-0.5 left-5 w-3 h-1 bg-cyan-200 rounded-sm opacity-60"></div>
              
              {/* Train nose */}
              <div className="absolute -right-2 top-0 w-4 h-4 bg-gradient-to-r from-slate-400 to-slate-500 rounded-r-full"></div>
            </div>
            
            {/* Compact wheels */}
            <div className={`absolute -bottom-0.5 left-1 w-2 h-2 bg-slate-600 rounded-full ${isActive ? 'animate-spin' : ''}`} style={{ animationDuration: '0.1s' }}></div>
            <div className={`absolute -bottom-0.5 left-4 w-2 h-2 bg-slate-600 rounded-full ${isActive ? 'animate-spin' : ''}`} style={{ animationDuration: '0.1s' }}></div>
          </div>
        </div>

        {/* Wind effect lines */}
        {isActive && (
          <div className="absolute inset-0">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="absolute animate-pulse"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${35 + (i % 2) * 30}%`,
                  animationDelay: `${i * 0.2}s`
                }}
              >
                <div className="w-4 h-0.5 bg-cyan-400 opacity-60"></div>
              </div>
            ))}
          </div>
        )}

        {/* Status indicator */}
        <div className="absolute top-2 right-2">
          <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-emerald-400 animate-pulse' : 'bg-slate-500'}`}></div>
        </div>
      </div>

      {/* Compact info overlay */}
      <div className="absolute bottom-1 left-2 text-xs text-slate-400">
        Tunnel Wind Capture
      </div>
      
      <div className="absolute bottom-1 right-2 text-xs text-emerald-400">
        {isActive ? 'Active' : 'Standby'}
      </div>
    </div>
  );
}
