import AnimatedSection from "@/components/AnimatedSection";
import StaggeredGrid from "@/components/StaggeredGrid";
import TopDownDemo from "@/components/TopDownDemo";
import PerformanceDashboard from "@/components/PerformanceDashboard";
import ROICalculator from "@/components/ROICalculator";
import TechnicalPerformanceCharts from "@/components/TechnicalPerformanceCharts";
import SectionNavigator from "@/components/SectionNavigator";


export default function ProductPage() {
  const navigationSections = [
    {
      id: 'hero',
      label: 'Overview',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      id: 'performance',
      label: 'Live Analytics',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      id: 'roi-calculator',
      label: 'ROI Calculator',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      id: 'technical-charts',
      label: 'Technical Data',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1-2H9l-1 2H5V5z" clipRule="evenodd" />
        </svg>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      <SectionNavigator sections={navigationSections} />
      {/* Hero Section */}
      <section id="hero" className="gradient-tunnel text-white py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <AnimatedSection animation="bounce-in">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">
                Advanced Savonius
                <span className="block text-cyan-300 animate-pulse-glow">Tunnel Technology</span>
              </h1>
            </AnimatedSection>

            <AnimatedSection animation="fade-up" delay={300}>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-slate-200">
                Precision-engineered vertical-axis wind turbines optimized for high-speed rail tunnel environments
              </p>
            </AnimatedSection>

            <AnimatedSection animation="zoom-in" delay={600}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-slate-800 px-8 py-3 rounded-lg font-semibold hover:bg-slate-100 transition-all duration-300 hover-lift hover-glow shadow-industrial">
                  Technical Specifications
                </button>
                <button className="border-2 border-cyan-300 text-cyan-300 px-8 py-3 rounded-lg font-semibold hover:bg-cyan-300 hover:text-slate-800 transition-all duration-300 hover-lift">
                  Watch Demo Video
                </button>
              </div>
            </AnimatedSection>
          </div>
        </div>

        {/* Animated background elements - industrial themed */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-10 left-10 w-16 h-16 bg-slate-400 bg-opacity-20 rounded-full animate-float"></div>
          <div className="absolute top-20 right-20 w-12 h-12 bg-cyan-400 bg-opacity-20 rounded-full animate-float animate-delay-200"></div>
          <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-slate-300 bg-opacity-20 rounded-full animate-float animate-delay-400"></div>
          <div className="absolute bottom-10 right-10 w-14 h-14 bg-emerald-400 bg-opacity-20 rounded-full animate-rotate-slow"></div>
        </div>
      </section>

      {/* Product Overview */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection animation="fade-right">
              <div>
                <h2 className="text-4xl font-bold text-slate-800 mb-6">
                  Industrial Savonius Engineering
                </h2>
                <p className="text-lg text-slate-600 mb-6">
                  Our precision-engineered Savonius turbine represents advanced vertical-axis wind technology,
                  specifically designed for tunnel environments and high-speed rail infrastructure integration.
                </p>
                <StaggeredGrid
                  staggerDelay={150}
                  animation="fade-left"
                  className="space-y-4"
                >
                  <div className="flex items-center space-x-3 hover-lift">
                    <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse-glow"></div>
                    <span className="text-slate-700">Vertical-axis design optimized for tunnel airflow patterns</span>
                  </div>
                  <div className="flex items-center space-x-3 hover-lift">
                    <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse-glow"></div>
                    <span className="text-slate-700">Low-vibration operation suitable for rail infrastructure</span>
                  </div>
                  <div className="flex items-center space-x-3 hover-lift">
                    <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse-glow"></div>
                    <span className="text-slate-700">Industrial-grade construction for tunnel environments</span>
                  </div>
                  <div className="flex items-center space-x-3 hover-lift">
                    <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse-glow"></div>
                    <span className="text-slate-700">Aerodynamic blade geometry for maximum wind capture</span>
                  </div>
                </StaggeredGrid>
              </div>
            </AnimatedSection>

            <AnimatedSection animation="fade-left" delay={300}>
              <div className="gradient-rail rounded-2xl p-8 hover-lift tunnel-depth">
                <div className="text-center">
                  <div className="w-48 h-48 gradient-wind rounded-full mx-auto mb-6 flex items-center justify-center animate-float hover-glow shadow-steel">
                    <div className="w-32 h-32 border-4 border-white rounded-full flex items-center justify-center">
                      <svg className="w-16 h-16 text-white animate-rotate-slow" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L8 8h8l-4-6zm0 20l4-6H8l4 6zm-6-8L2 12l4-2v4zm12 0v-4l4 2-4 2z"/>
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Tunnel Turbine System</h3>
                  <p className="text-slate-200">
                    Advanced helical Savonius design with precision-engineered blade curvature for optimal tunnel wind capture
                  </p>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>



      {/* Technical Specifications */}
      <section className="py-20 bg-slate-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Engineering Specifications
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Precision-engineered for optimal performance in tunnel rail environments
              </p>
            </div>
          </AnimatedSection>

          <StaggeredGrid
            staggerDelay={200}
            animation="zoom-in"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <div className="bg-white rounded-xl p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
              <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                <div className="w-3 h-3 bg-emerald-600 rounded-full mr-3 animate-pulse-glow"></div>
                Power Generation
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Rated Power:</span>
                  <span className="font-semibold text-slate-800">5-15 kW</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Peak Power:</span>
                  <span className="font-semibold text-slate-800">25 kW</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Cut-in Speed:</span>
                  <span className="font-semibold text-slate-800">3 m/s</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Rated Speed:</span>
                  <span className="font-semibold text-slate-800">12 m/s</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
              <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                <div className="w-3 h-3 bg-cyan-600 rounded-full mr-3 animate-pulse-glow"></div>
                Physical Specifications
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Height:</span>
                  <span className="font-semibold text-slate-800">1 meters</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Diameter:</span>
                  <span className="font-semibold text-slate-800">0.2 meters</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Weight:</span>
                  <span className="font-semibold text-slate-800">2,500 kg</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Material:</span>
                  <span className="font-semibold text-slate-800">Industrial Aluminum</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-industrial hover-lift hover-glow-steel transition-all duration-300">
              <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                <div className="w-3 h-3 bg-slate-600 rounded-full mr-3 animate-pulse-glow"></div>
                Operational Performance
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Efficiency:</span>
                  <span className="font-semibold text-slate-800">35-40%</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Noise Level:</span>
                  <span className="font-semibold text-slate-800">&lt;45 dB</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Service Life:</span>
                  <span className="font-semibold text-slate-800">25+ years</span>
                </div>
                <div className="flex justify-between hover-scale">
                  <span className="text-slate-600">Maintenance:</span>
                  <span className="font-semibold text-slate-800">Minimal</span>
                </div>
              </div>
            </div>
          </StaggeredGrid>
        </div>
      </section>

      {/* Top-Down Perspective Demo */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Top-Down Perspective View
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Experience our tunnel wind capture system from a bird&apos;s eye perspective, showing the complete train trajectory and turbine activation sequence
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={300}>
            <TopDownDemo className="max-w-4xl mx-auto" autoPlay={true} />
          </AnimatedSection>

          <AnimatedSection animation="fade-up" delay={600}>
            <div className="mt-12 text-center">
              <p className="text-slate-600 mb-6">
                This top-down demonstration shows the diagonal train trajectory with realistic perspective scaling,
                from distant approach to close passage, activating turbines along the tunnel walls.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="w-12 h-12 bg-cyan-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                    <svg className="w-6 h-6 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-slate-800 mb-2">Diagonal Trajectory</h3>
                  <p className="text-sm text-slate-600">Train moves from upper-left background to lower-right foreground</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-emerald-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                    <svg className="w-6 h-6 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-slate-800 mb-2">Scale Progression</h3>
                  <p className="text-sm text-slate-600">Realistic perspective scaling from 0.4x to 1.2x size</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-slate-200 rounded-full mx-auto mb-3 flex items-center justify-center">
                    <svg className="w-6 h-6 text-slate-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-slate-800 mb-2">Turbine Activation</h3>
                  <p className="text-sm text-slate-600">Sequential turbine spinning based on train proximity</p>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>



      {/* Product Features & Applications */}
      <section className="py-20 bg-slate-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Engineering Features & Infrastructure Applications
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Industrial-grade design optimized for rail infrastructure and tunnel deployment scenarios
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <AnimatedSection animation="fade-right">
              <div>
                <h3 className="text-2xl font-bold text-slate-800 mb-6">Advanced Engineering Features</h3>
                <StaggeredGrid
                  staggerDelay={200}
                  animation="fade-left"
                  className="space-y-6"
                >
                  <div className="flex items-start space-x-4 hover-lift">
                    <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0 animate-float">
                      <svg className="w-6 h-6 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800 mb-2">Tunnel-Optimized Wind Capture</h4>
                      <p className="text-slate-600">Vertical-axis design specifically engineered for confined tunnel airflow patterns and high-velocity wind capture.</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 hover-lift">
                    <div className="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center flex-shrink-0 animate-float animate-delay-200">
                      <svg className="w-6 h-6 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800 mb-2">Industrial-Grade Construction</h4>
                      <p className="text-slate-600">Heavy-duty materials and precision engineering ensure reliable operation in demanding rail infrastructure environments.</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 hover-lift">
                    <div className="w-12 h-12 bg-slate-200 rounded-lg flex items-center justify-center flex-shrink-0 animate-float animate-delay-400">
                      <svg className="w-6 h-6 text-slate-600 animate-pulse-glow" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                        <path fillRule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800 mb-2">Integrated Monitoring Systems</h4>
                      <p className="text-slate-600">Advanced IoT sensors and control systems provide real-time performance data and predictive maintenance capabilities.</p>
                    </div>
                  </div>
                </StaggeredGrid>
              </div>
            </AnimatedSection>

            <AnimatedSection animation="fade-left" delay={300}>
              <div>
                <h3 className="text-2xl font-bold text-slate-800 mb-6">Infrastructure Applications</h3>
                <StaggeredGrid
                  staggerDelay={150}
                  animation="zoom-in"
                  className="space-y-6"
                >
                  <div className="bg-white rounded-lg p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
                    <h4 className="text-lg font-semibold text-slate-800 mb-2">High-Speed Rail Tunnels</h4>
                    <p className="text-slate-600">Primary deployment within tunnel infrastructure to capture concentrated wind energy from high-velocity train passage.</p>
                  </div>

                  <div className="bg-white rounded-lg p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
                    <h4 className="text-lg font-semibold text-slate-800 mb-2">Metro & Transit Systems</h4>
                    <p className="text-slate-600">Integration with urban rail networks and underground transit systems for distributed energy generation.</p>
                  </div>

                  <div className="bg-white rounded-lg p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
                    <h4 className="text-lg font-semibold text-slate-800 mb-2">Transportation Corridors</h4>
                    <p className="text-slate-600">Strategic installation along major rail corridors and transportation hubs for grid-scale energy contribution.</p>
                  </div>

                  <div className="bg-white rounded-lg p-6 shadow-industrial hover-lift hover-glow transition-all duration-300">
                    <h4 className="text-lg font-semibold text-slate-800 mb-2">Smart Infrastructure Networks</h4>
                    <p className="text-slate-600">Integration with intelligent transportation systems and smart grid infrastructure for optimized energy management.</p>
                  </div>
                </StaggeredGrid>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Performance Dashboard Section */}
      <section id="performance" className="section-spacing-large bg-gradient-to-br from-slate-50 to-white">
        <div className="container-wide">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16 lg:mb-20">
              <div className="flex items-center justify-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent flex-1 max-w-xs"></div>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
                Live Performance Analytics
              </h2>
              <p className="text-xl lg:text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
                Real-time monitoring and comprehensive performance data from our tunnel wind capture systems
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={300}>
            <PerformanceDashboard className="mb-8" />
          </AnimatedSection>
        </div>
      </section>

      {/* ROI Calculator Section */}
      <section id="roi-calculator" className="section-spacing-large bg-gradient-to-br from-slate-100 to-slate-50">
        <div className="container-wide">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Investment Analysis & ROI Calculator
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Calculate the financial returns and business case for tunnel wind capture technology implementation
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={300}>
            <ROICalculator className="mb-8" />
          </AnimatedSection>
        </div>
      </section>

      {/* Technical Performance Charts Section */}
      <section id="technical-charts" className="section-spacing-large bg-gradient-to-br from-white to-slate-50">
        <div className="container-wide">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Technical Performance Analysis
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Comprehensive technical data, efficiency metrics, and comparative analysis with other renewable energy technologies
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={300}>
            <TechnicalPerformanceCharts className="mb-8" />
          </AnimatedSection>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-br from-slate-800 to-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection animation="fade-up">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Transform Your Infrastructure?
            </h2>
            <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
              Join the sustainable energy revolution with our advanced tunnel wind capture technology
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl hover-lift">
                Request Technical Consultation
              </button>
              <button className="border-2 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-slate-900 font-semibold py-4 px-8 rounded-lg transition-all duration-300 hover-lift">
                Download Specifications
              </button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
}
