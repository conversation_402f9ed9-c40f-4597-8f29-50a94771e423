'use client';

import { useState } from 'react';
import TunnelAnimation from './TunnelAnimation';
import AnimatedSection from './AnimatedSection';

export default function TechnologyDemo() {
  const [isPlaying, setIsPlaying] = useState(true);

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <AnimatedSection animation="fade-up">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technology in Action
            </h2>
            <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-8">
              Watch our advanced Savonius turbine system capture wind energy from high-speed trains 
              traveling through tunnel infrastructure
            </p>
            <div className="flex justify-center space-x-4 text-sm text-slate-400">
              <span className="flex items-center">
                <div className="w-3 h-3 bg-cyan-400 rounded-full mr-2"></div>
                Wind Flow
              </span>
              <span className="flex items-center">
                <div className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></div>
                Energy Generation
              </span>
              <span className="flex items-center">
                <div className="w-3 h-3 bg-slate-400 rounded-full mr-2"></div>
                Turbine Rotation
              </span>
            </div>
          </div>
        </AnimatedSection>

        {/* Main Animation Container */}
        <AnimatedSection animation="zoom-in" delay={300}>
          <div className="relative">
            {/* Animation Frame */}
            <div className="bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl p-8 shadow-2xl border border-slate-700">
              <div className="relative h-96 rounded-xl overflow-hidden">
                <TunnelAnimation 
                  className="w-full h-full"
                  autoPlay={isPlaying}
                  showControls={false}
                />
                
                {/* Overlay Information */}
                <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-80 rounded-lg p-3 text-white text-sm">
                  <div className="font-semibold mb-1">Live Demonstration</div>
                  <div className="text-slate-300">High-Speed Rail Tunnel</div>
                </div>
                
                <div className="absolute top-4 right-4 bg-slate-800 bg-opacity-80 rounded-lg p-3 text-white text-sm">
                  <div className="font-semibold mb-1">Speed: 300 km/h</div>
                  <div className="text-slate-300">Wind Capture: Active</div>
                </div>
                
                <div className="absolute bottom-4 left-4 bg-slate-800 bg-opacity-80 rounded-lg p-3 text-white text-sm">
                  <div className="font-semibold mb-1">Power Output</div>
                  <div className="text-emerald-400">15 kW Generated</div>
                </div>
              </div>
              
              {/* Animation Controls */}
              <div className="flex justify-center mt-6">
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="bg-gradient-to-r from-cyan-600 to-cyan-700 text-white px-8 py-3 rounded-lg font-semibold hover:from-cyan-700 hover:to-cyan-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  {isPlaying ? (
                    <span className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Pause Demo
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      Play Demo
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Technical Explanation */}
        <AnimatedSection animation="fade-up" delay={600}>
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Wind Capture</h3>
              <p className="text-slate-400">
                High-speed trains create powerful, predictable wind currents in tunnel environments
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Energy Conversion</h3>
              <p className="text-slate-400">
                Savonius turbines efficiently convert tunnel airflow into clean electrical energy
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Grid Integration</h3>
              <p className="text-slate-400">
                Generated power integrates seamlessly with rail infrastructure and regional grids
              </p>
            </div>
          </div>
        </AnimatedSection>

        {/* Performance Metrics */}
        <AnimatedSection animation="fade-up" delay={900}>
          <div className="mt-16 bg-gradient-to-r from-slate-800 to-slate-700 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white text-center mb-8">Real-Time Performance Metrics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400 mb-2">300</div>
                <div className="text-slate-400 text-sm">km/h Train Speed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-emerald-400 mb-2">15</div>
                <div className="text-slate-400 text-sm">kW Power Output</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">85%</div>
                <div className="text-slate-400 text-sm">Capture Efficiency</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">24/7</div>
                <div className="text-slate-400 text-sm">Operation Time</div>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
