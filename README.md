# Tunnel Wind Capture Technology

🚀 **Live Website**: [https://aerotransit-lac.vercel.app/](https://aerotransit-lac.vercel.app/)

A professional Next.js application showcasing advanced Savonius turbine technology for capturing wind energy from high-speed trains traveling through tunnels.

## Latest Enhancements (Deployed)
- ✅ **Enhanced TunnelAnimation**: Realistic Savonius turbine rotation around central axis
- ✅ **Professional Team Section**: 5-person engineering team showcase
- ✅ **Optimized Train Movement**: Continuous looping with enhanced silver-white body and cyan accent stripes
- ✅ **Fixed TopDownDemo**: Consistent train sizing with horizontal movement
- ✅ **Responsive Turbine Activation**: Real-time activation based on train position
- ✅ **Corporate Presentation Quality**: Professional styling for investor demonstrations

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
