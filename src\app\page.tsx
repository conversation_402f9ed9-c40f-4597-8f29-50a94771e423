import Link from "next/link";
import AnimatedSection from "@/components/AnimatedSection";
import StaggeredGrid from "@/components/StaggeredGrid";
import TechnologyDemo from "@/components/TechnologyDemo";
import PerformanceDashboard from "@/components/PerformanceDashboard";
import CompactTunnelDemo from "@/components/CompactTunnelDemo";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative overflow-hidden gradient-tunnel min-h-[90vh] flex items-center">
        <div className="container-wide py-20 lg:py-32">
          <div className="text-center">
            <AnimatedSection animation="bounce-in">
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                Revolutionizing
                <span className="text-cyan-300 block animate-pulse-glow">Rail Energy Capture</span>
              </h1>
            </AnimatedSection>

            <AnimatedSection animation="fade-up" delay={300}>
              <p className="text-xl md:text-2xl text-slate-200 mb-8 max-w-3xl mx-auto">
                Harness wind power from high-speed trains traveling through tunnels with our advanced Savonius turbine technology
              </p>
            </AnimatedSection>

            <AnimatedSection animation="zoom-in" delay={600}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="gradient-wind text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover-lift hover-glow shadow-industrial">
                  Learn More
                </button>
                <Link href="/product" className="border-2 border-cyan-300 text-cyan-300 px-8 py-3 rounded-lg font-semibold hover:bg-cyan-300 hover:text-slate-800 transition-all duration-300 text-center hover-lift">
                  View Product
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </div>

        {/* Enhanced animated background elements - tunnel/rail themed */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
          <div className="absolute top-20 left-10 w-20 h-20 bg-slate-400 bg-opacity-20 rounded-full animate-float"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-cyan-400 bg-opacity-20 rounded-full animate-float animate-delay-200"></div>
          <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-slate-300 bg-opacity-20 rounded-full animate-float animate-delay-400"></div>
          <div className="absolute top-1/2 right-1/3 w-12 h-12 bg-blue-400 bg-opacity-20 rounded-full animate-rotate-slow"></div>
          <div className="absolute bottom-1/3 right-10 w-18 h-18 bg-emerald-400 bg-opacity-20 rounded-full animate-float animate-delay-600"></div>
        </div>
      </section>

      {/* Concept Introduction Section */}
      <section className="section-spacing-large bg-white">
        <div className="container-content">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Advanced Tunnel Wind Capture Technology
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Transforming high-speed rail tunnel airflow into clean, renewable electricity through innovative engineering
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection animation="fade-right">
              <div>
                <h3 className="text-2xl font-bold text-slate-800 mb-6">
                  How Tunnel Wind Capture Works
                </h3>

                {/* Compact Demo */}
                <div className="mb-8">
                  <CompactTunnelDemo className="h-24 w-full" autoPlay={true} />
                </div>

                <StaggeredGrid
                  staggerDelay={150}
                  animation="fade-left"
                  className="space-y-4"
                >
                  <div className="flex items-start space-x-3 hover-lift">
                    <div className="w-6 h-6 gradient-wind rounded-full flex items-center justify-center flex-shrink-0 mt-1 animate-pulse-glow">
                      <span className="text-white text-sm font-bold">1</span>
                    </div>
                    <p className="text-slate-600">
                      <strong>High-Speed Transit:</strong> HSR trains traveling at 200-350 km/h through tunnels create concentrated, predictable wind currents.
                    </p>
                  </div>
                  <div className="flex items-start space-x-3 hover-lift">
                    <div className="w-6 h-6 gradient-wind rounded-full flex items-center justify-center flex-shrink-0 mt-1 animate-pulse-glow">
                      <span className="text-white text-sm font-bold">2</span>
                    </div>
                    <p className="text-slate-600">
                      <strong>Strategic Positioning:</strong> Savonius turbines are optimally placed within tunnel infrastructure to maximize wind capture efficiency.
                    </p>
                  </div>
                  <div className="flex items-start space-x-3 hover-lift">
                    <div className="w-6 h-6 gradient-wind rounded-full flex items-center justify-center flex-shrink-0 mt-1 animate-pulse-glow">
                      <span className="text-white text-sm font-bold">3</span>
                    </div>
                    <p className="text-slate-600">
                      <strong>Energy Conversion:</strong> Advanced vertical-axis turbine technology efficiently converts tunnel airflow into electrical energy.
                    </p>
                  </div>
                  <div className="flex items-start space-x-3 hover-lift">
                    <div className="w-6 h-6 gradient-wind rounded-full flex items-center justify-center flex-shrink-0 mt-1 animate-pulse-glow">
                      <span className="text-white text-sm font-bold">4</span>
                    </div>
                    <p className="text-slate-600">
                      <strong>Infrastructure Integration:</strong> Generated power integrates seamlessly with existing rail electrical systems and regional grids.
                    </p>
                  </div>
                </StaggeredGrid>
              </div>
            </AnimatedSection>

            <AnimatedSection animation="fade-left" delay={300}>
              <div className="gradient-rail rounded-2xl p-8 hover-lift tunnel-depth">
                <div className="text-center">
                  <div className="w-32 h-32 gradient-wind rounded-full mx-auto mb-6 flex items-center justify-center animate-float hover-glow shadow-steel">
                    <svg className="w-16 h-16 text-white animate-rotate-slow" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="text-2xl font-bold text-white mb-4">Tunnel Energy Harvesting</h4>
                  <p className="text-slate-200">
                    Every train passage through tunnel infrastructure generates renewable energy, transforming transportation corridors into sustainable power sources.
                  </p>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Technology Demonstration Section */}
      <TechnologyDemo />

      {/* Environmental Benefits Section */}
      <section className="section-spacing bg-slate-100">
        <div className="container-content">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Infrastructure & Environmental Benefits
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Maximizing existing transportation infrastructure for sustainable energy generation
              </p>
            </div>
          </AnimatedSection>

          <StaggeredGrid
            staggerDelay={200}
            animation="zoom-in"
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            <div className="bg-white rounded-xl p-8 shadow-industrial text-center hover-lift hover-glow-green transition-all duration-300">
              <div className="w-16 h-16 bg-emerald-100 rounded-full mx-auto mb-6 flex items-center justify-center animate-float">
                <svg className="w-8 h-8 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Zero Emissions</h3>
              <p className="text-slate-600">
                Clean energy generation with no harmful emissions, contributing to reduced carbon footprint in transportation corridors.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-industrial text-center hover-lift hover-glow transition-all duration-300">
              <div className="w-16 h-16 bg-cyan-100 rounded-full mx-auto mb-6 flex items-center justify-center animate-float animate-delay-200">
                <svg className="w-8 h-8 text-cyan-600 animate-rotate-slow" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Infrastructure Efficiency</h3>
              <p className="text-slate-600">
                Maximizes existing rail infrastructure investment by adding energy generation capability to transportation systems.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300">
              <div className="w-16 h-16 bg-slate-200 rounded-full mx-auto mb-6 flex items-center justify-center animate-float animate-delay-400">
                <svg className="w-8 h-8 text-slate-600 animate-pulse-glow" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">Industrial Innovation</h3>
              <p className="text-slate-600">
                Advanced engineering solution that transforms transportation infrastructure into sustainable energy assets.
              </p>
            </div>
          </StaggeredGrid>
        </div>
      </section>

      {/* Performance Metrics Preview */}
      <section className="section-spacing-large bg-white">
        <div className="container-wide">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Real-Time Performance Monitoring
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Experience live data from our tunnel wind capture technology demonstration systems
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={300}>
            <PerformanceDashboard className="mb-8" />
          </AnimatedSection>
        </div>
      </section>

      {/* Team Members Section */}
      <section className="py-20 bg-gradient-to-br from-slate-800 to-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection animation="fade-up">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Our Engineering Team
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Meet the innovative minds behind tunnel wind capture technology, bringing together expertise in renewable energy, rail infrastructure, and advanced engineering
              </p>
            </div>
          </AnimatedSection>

          <StaggeredGrid
            staggerDelay={150}
            animation="zoom-in"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6"
          >
            {/* Team Member 1 - Project Lead */}
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-6 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300 border border-slate-600">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto flex items-center justify-center shadow-steel">
                  <svg className="w-12 h-12 text-cyan-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-cyan-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Dr. Sarah Chen</h3>
              <p className="text-cyan-300 text-sm font-semibold mb-3">Project Lead & Systems Engineer</p>
              <p className="text-slate-300 text-sm leading-relaxed">
                15+ years in renewable energy systems and rail infrastructure integration. PhD in Mechanical Engineering with focus on wind energy harvesting.
              </p>
            </div>

            {/* Team Member 2 - Aerodynamics Specialist */}
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-6 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300 border border-slate-600">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto flex items-center justify-center shadow-steel">
                  <svg className="w-12 h-12 text-emerald-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Marcus Rodriguez</h3>
              <p className="text-emerald-300 text-sm font-semibold mb-3">Aerodynamics Specialist</p>
              <p className="text-slate-300 text-sm leading-relaxed">
                Expert in computational fluid dynamics and turbine design. 12 years optimizing wind capture systems for transportation environments.
              </p>
            </div>

            {/* Team Member 3 - Infrastructure Engineer */}
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-6 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300 border border-slate-600">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto flex items-center justify-center shadow-steel">
                  <svg className="w-12 h-12 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Elena Kowalski</h3>
              <p className="text-blue-300 text-sm font-semibold mb-3">Infrastructure Engineer</p>
              <p className="text-slate-300 text-sm leading-relaxed">
                Specialist in rail tunnel construction and structural integration. 10 years designing sustainable infrastructure solutions for transportation systems.
              </p>
            </div>

            {/* Team Member 4 - Electrical Systems */}
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-6 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300 border border-slate-600">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto flex items-center justify-center shadow-steel">
                  <svg className="w-12 h-12 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">James Thompson</h3>
              <p className="text-yellow-300 text-sm font-semibold mb-3">Electrical Systems Engineer</p>
              <p className="text-slate-300 text-sm leading-relaxed">
                Power systems integration expert with focus on grid connectivity and energy storage. 8 years in renewable energy electrical infrastructure.
              </p>
            </div>

            {/* Team Member 5 - Business Development */}
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-6 shadow-industrial text-center hover-lift hover-glow-steel transition-all duration-300 border border-slate-600">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-500 to-slate-600 rounded-full mx-auto flex items-center justify-center shadow-steel">
                  <svg className="w-12 h-12 text-purple-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Priya Patel</h3>
              <p className="text-purple-300 text-sm font-semibold mb-3">Business Development Lead</p>
              <p className="text-slate-300 text-sm leading-relaxed">
                Infrastructure partnerships and market strategy specialist. 7 years connecting innovative technologies with transportation industry stakeholders.
              </p>
            </div>
          </StaggeredGrid>

          {/* Team Collaboration Highlight */}
          <AnimatedSection animation="fade-up" delay={800}>
            <div className="mt-16 text-center">
              <div className="bg-gradient-to-r from-slate-700 to-slate-800 rounded-2xl p-8 shadow-industrial border border-slate-600">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-cyan-500 rounded-full flex items-center justify-center shadow-steel">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Collaborative Innovation</h3>
                <p className="text-slate-300 text-lg max-w-3xl mx-auto">
                  Our multidisciplinary team combines decades of experience in renewable energy, rail infrastructure, and business development to deliver cutting-edge tunnel wind capture solutions for the transportation industry.
                </p>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 gradient-eco-professional">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection animation="bounce-in">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Explore Our Rail Energy Technology?
            </h2>
          </AnimatedSection>

          <AnimatedSection animation="fade-up" delay={300}>
            <p className="text-xl text-cyan-100 mb-8 max-w-3xl mx-auto">
              Discover the technical specifications and engineering innovations of our tunnel wind capture system
            </p>
          </AnimatedSection>

          <AnimatedSection animation="zoom-in" delay={600}>
            <Link
              href="/product"
              className="bg-white text-slate-800 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-slate-100 transition-all duration-300 inline-block hover-lift hover-glow shadow-steel"
            >
              Explore Technical Details →
            </Link>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
}
