'use client';

import { useEffect, useRef, useState } from 'react';

interface TunnelAnimationProps {
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
}

export default function TunnelAnimation({
  className = '',
  autoPlay = true,
  showControls = false
}: TunnelAnimationProps) {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [animationCycle, setAnimationCycle] = useState(0);
  const [trainPosition, setTrainPosition] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Enhanced continuous train animation loop with better coordination
  useEffect(() => {
    if (isPlaying) {
      const trainInterval = setInterval(() => {
        setTrainPosition(prev => {
          const newPosition = prev + 0.4; // Slightly slower for better turbine coordination
          return newPosition >= 100 ? -20 : newPosition; // Reset to start when reaching end
        });
      }, 50); // 20 FPS for smoother coordination with turbines

      return () => clearInterval(trainInterval);
    }
  }, [isPlaying]);

  // Overall animation cycle for turbines and effects
  useEffect(() => {
    if (isPlaying) {
      const cycleInterval = setInterval(() => {
        setAnimationCycle(prev => prev + 1);
      }, 8000); // Complete cycle every 8 seconds

      return () => clearInterval(cycleInterval);
    }
  }, [isPlaying]);

  // Enhanced turbine activity calculation with proper timing coordination
  const getTurbineActivity = (turbineIndex: number) => {
    // More accurate turbine positioning along the tunnel
    const turbinePosition = 15 + (turbineIndex * 12); // Better spaced positions
    const distance = Math.abs(trainPosition - turbinePosition);

    // Improved activation logic with approach and departure phases
    const activationRange = 15; // Activation distance
    const isActive = distance < activationRange;

    // Add slight delay for more realistic wind capture timing
    const windDelay = trainPosition > turbinePosition ? 2 : 0; // Slight delay after train passes
    const adjustedDistance = distance + windDelay;

    return adjustedDistance < activationRange ? 1 : 0;
  };

  const toggleAnimation = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <div className={`relative overflow-hidden ${className}`} ref={containerRef}>
      {/* Tunnel Structure */}
      <div className="absolute inset-0 gradient-tunnel">
        {/* Tunnel Walls with Perspective */}
        <div className="absolute inset-0">
          {/* Top tunnel arch */}
          <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-b from-slate-800 to-slate-700 rounded-b-full transform perspective-1000 rotateX-5"></div>
          
          {/* Bottom tunnel floor */}
          <div className="absolute bottom-0 left-0 w-full h-1/4 bg-gradient-to-t from-slate-900 to-slate-800"></div>
          
          {/* Tunnel depth lines for perspective */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="absolute bg-slate-600 opacity-20"
                style={{
                  left: `${5 + i * 4.5}%`,
                  top: '30%',
                  width: '2px',
                  height: '40%',
                  transform: `scaleY(${0.3 + i * 0.035})`,
                  transformOrigin: 'center bottom'
                }}
              />
            ))}
          </div>
        </div>

        {/* Savonius Turbines */}
        <div className="absolute inset-0">
          {/* Left side turbines */}
          {[...Array(4)].map((_, i) => (
            <div
              key={`left-${i}`}
              className="absolute"
              style={{
                left: `${15 + i * 20}%`,
                top: '45%',
                transform: 'translateY(-50%)'
              }}
            >
              <div className="w-8 h-12 relative">
                {/* Stationary turbine housing */}
                <div className="absolute inset-0 bg-gradient-to-b from-slate-300 to-slate-400 rounded-lg opacity-20 border border-slate-400"></div>

                {/* Rotating S-shaped blades - spin around central axis */}
                <div className={`absolute inset-0 ${isPlaying && getTurbineActivity(i) ? 'animate-spin' : ''}`}
                     style={{
                       animationDuration: '1.2s',
                       animationDelay: '0s',
                       animationTimingFunction: 'linear',
                       animationIterationCount: 'infinite',
                       transformOrigin: 'center center'
                     }}>
                  {/* Enhanced Savonius S-shaped blade - accurate helical design */}
                  <div className="absolute inset-0">
                    {/* Improved S-shaped blade with proper helical configuration */}
                    <div className="absolute inset-0">
                      {/* First S-curve blade section */}
                      <div className="absolute top-0 left-1 w-3 h-5 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-l-full opacity-90 shadow-md"></div>
                      <div className="absolute bottom-0 right-1 w-3 h-5 bg-gradient-to-tl from-cyan-400 to-cyan-600 rounded-r-full opacity-90 shadow-md"></div>

                      {/* Connecting spine for structural realism */}
                      <div className="absolute top-2 left-2 w-4 h-8 bg-gradient-to-b from-cyan-500 to-cyan-600 opacity-75 transform rotate-12 rounded-sm"></div>

                      {/* Second S-curve blade section (offset for helical effect) */}
                      <div className="absolute top-1 right-1 w-3 h-5 bg-gradient-to-bl from-cyan-300 to-cyan-500 rounded-r-full opacity-80 shadow-sm"></div>
                      <div className="absolute bottom-1 left-1 w-3 h-5 bg-gradient-to-tr from-cyan-300 to-cyan-500 rounded-l-full opacity-80 shadow-sm"></div>
                    </div>
                  </div>
                </div>

                {/* Stationary central hub - does not rotate */}
                <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2 border border-slate-500 z-10"></div>
              </div>
              
              {/* Enhanced energy generation indicator */}
              <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 ${isPlaying && getTurbineActivity(i) ? 'animate-pulse' : ''}`}>
                <div className={`w-2 h-2 rounded-full shadow-lg transition-all duration-300 ${
                  getTurbineActivity(i)
                    ? 'bg-emerald-400 shadow-emerald-400/50 animate-ping'
                    : 'bg-slate-500 shadow-slate-500/30'
                }`}></div>
              </div>
            </div>
          ))}

          {/* Right side turbines */}
          {[...Array(4)].map((_, i) => (
            <div
              key={`right-${i}`}
              className="absolute"
              style={{
                right: `${15 + i * 20}%`,
                top: '55%',
                transform: 'translateY(-50%)'
              }}
            >
              <div className="w-8 h-12 relative">
                {/* Stationary turbine housing */}
                <div className="absolute inset-0 bg-gradient-to-b from-slate-300 to-slate-400 rounded-lg opacity-20 border border-slate-400"></div>

                {/* Rotating S-shaped blades - spin around central axis */}
                <div className={`absolute inset-0 ${isPlaying && getTurbineActivity(i + 4) ? 'animate-spin' : ''}`}
                     style={{
                       animationDuration: '1.4s',
                       animationDelay: '0s',
                       animationTimingFunction: 'linear',
                       animationDirection: 'reverse',
                       animationIterationCount: 'infinite',
                       transformOrigin: 'center center'
                     }}>
                  {/* Enhanced Savonius S-shaped blade - accurate helical design */}
                  <div className="absolute inset-0">
                    {/* Improved S-shaped blade with proper helical configuration */}
                    <div className="absolute inset-0">
                      {/* First S-curve blade section */}
                      <div className="absolute top-0 left-1 w-3 h-5 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-l-full opacity-90 shadow-md"></div>
                      <div className="absolute bottom-0 right-1 w-3 h-5 bg-gradient-to-tl from-cyan-400 to-cyan-600 rounded-r-full opacity-90 shadow-md"></div>

                      {/* Connecting spine for structural realism */}
                      <div className="absolute top-2 left-2 w-4 h-8 bg-gradient-to-b from-cyan-500 to-cyan-600 opacity-75 transform rotate-12 rounded-sm"></div>

                      {/* Second S-curve blade section (offset for helical effect) */}
                      <div className="absolute top-1 right-1 w-3 h-5 bg-gradient-to-bl from-cyan-300 to-cyan-500 rounded-r-full opacity-80 shadow-sm"></div>
                      <div className="absolute bottom-1 left-1 w-3 h-5 bg-gradient-to-tr from-cyan-300 to-cyan-500 rounded-l-full opacity-80 shadow-sm"></div>
                    </div>
                  </div>
                </div>

                {/* Stationary central hub - does not rotate */}
                <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-gradient-to-br from-slate-400 to-slate-600 rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2 border border-slate-500 z-10"></div>
              </div>
              
              {/* Enhanced energy generation indicator */}
              <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 ${isPlaying && getTurbineActivity(i + 4) ? 'animate-pulse' : ''}`}>
                <div className={`w-2 h-2 rounded-full shadow-lg transition-all duration-300 ${
                  getTurbineActivity(i + 4)
                    ? 'bg-emerald-400 shadow-emerald-400/50 animate-ping'
                    : 'bg-slate-500 shadow-slate-500/30'
                }`}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced High-Speed Train with Continuous Looping Animation */}
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute top-1/2 transform -translate-y-1/2"
            style={{
              left: `${trainPosition}%`,
              transition: isPlaying ? 'none' : 'left 1s ease-out',
              transform: 'translateY(-50%)'
            }}
          >
            {/* Enhanced Motion Blur Effect */}
            {isPlaying && (
              <>
                {/* Primary motion blur */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-300 to-transparent opacity-40 blur-md transform scale-125 -translate-x-8"></div>
                {/* Secondary motion blur for speed effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-20 blur-lg transform scale-150 -translate-x-12"></div>
              </>
            )}

            {/* Enhanced Modern High-Speed Train Body - Professional Design */}
            <div className="relative">
              {/* Main train body - enhanced silver-white design with better contrast */}
              <div className="w-52 h-14 bg-gradient-to-r from-slate-50 via-slate-100 to-slate-200 rounded-r-3xl shadow-2xl relative border-2 border-slate-300">

                {/* Enhanced cyan accent stripes - more prominent and professional */}
                <div className="absolute top-2.5 left-0 right-10 h-2 bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 rounded-full shadow-lg"></div>
                <div className="absolute bottom-2.5 left-0 right-10 h-1 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full opacity-90 shadow-md"></div>

                {/* Enhanced train windows - modern design with better visibility */}
                <div className="absolute top-1.5 left-4 w-8 h-4.5 bg-gradient-to-b from-cyan-50 to-cyan-100 rounded-md border border-slate-300 opacity-95"></div>
                <div className="absolute top-1.5 left-14 w-8 h-4.5 bg-gradient-to-b from-cyan-50 to-cyan-100 rounded-md border border-slate-300 opacity-95"></div>
                <div className="absolute top-1.5 left-24 w-8 h-4.5 bg-gradient-to-b from-cyan-50 to-cyan-100 rounded-md border border-slate-300 opacity-95"></div>
                <div className="absolute top-1.5 left-34 w-8 h-4.5 bg-gradient-to-b from-cyan-50 to-cyan-100 rounded-md border border-slate-300 opacity-95"></div>

                {/* Enhanced aerodynamic nose - professional bullet train characteristic */}
                <div className="absolute -right-10 top-0 w-18 h-14 bg-gradient-to-r from-slate-200 via-slate-300 to-slate-400 rounded-r-full shadow-2xl border-r-2 border-slate-300">
                  {/* Enhanced nose tip highlight */}
                  <div className="absolute top-1 right-1 w-4 h-12 bg-gradient-to-r from-slate-100 to-transparent rounded-r-full opacity-80"></div>
                  {/* Enhanced front lights */}
                  <div className="absolute top-3 right-2 w-3 h-3 bg-yellow-300 rounded-full opacity-90 shadow-lg animate-pulse"></div>
                  <div className="absolute bottom-3 right-2 w-3 h-3 bg-red-400 rounded-full opacity-90 shadow-lg animate-pulse"></div>
                </div>

                {/* Pantograph - realistic high-speed rail equipment */}
                <div className="absolute -top-3 left-12 w-1 h-4 bg-slate-600 rounded-t"></div>
                <div className="absolute -top-4 left-10 w-5 h-1 bg-slate-600 rounded"></div>

                {/* Air conditioning units on roof */}
                <div className="absolute -top-1 left-8 w-3 h-1 bg-slate-400 rounded opacity-80"></div>
                <div className="absolute -top-1 left-20 w-3 h-1 bg-slate-400 rounded opacity-80"></div>
                <div className="absolute -top-1 left-32 w-3 h-1 bg-slate-400 rounded opacity-80"></div>
              </div>

              {/* Enhanced wheel system with realistic rotation */}
              <div className={`absolute -bottom-3 left-6 w-5 h-5 bg-slate-700 rounded-full shadow-lg border-2 border-slate-600 ${isPlaying ? 'animate-spin' : ''}`}
                   style={{ animationDuration: '0.08s', animationTimingFunction: 'linear' }}>
                <div className="absolute top-1 left-1 w-3 h-3 bg-slate-500 rounded-full"></div>
                <div className="absolute top-2 left-2 w-1 h-1 bg-slate-300 rounded-full"></div>
              </div>
              <div className={`absolute -bottom-3 left-16 w-5 h-5 bg-slate-700 rounded-full shadow-lg border-2 border-slate-600 ${isPlaying ? 'animate-spin' : ''}`}
                   style={{ animationDuration: '0.08s', animationTimingFunction: 'linear' }}>
                <div className="absolute top-1 left-1 w-3 h-3 bg-slate-500 rounded-full"></div>
                <div className="absolute top-2 left-2 w-1 h-1 bg-slate-300 rounded-full"></div>
              </div>
              <div className={`absolute -bottom-3 left-28 w-5 h-5 bg-slate-700 rounded-full shadow-lg border-2 border-slate-600 ${isPlaying ? 'animate-spin' : ''}`}
                   style={{ animationDuration: '0.08s', animationTimingFunction: 'linear' }}>
                <div className="absolute top-1 left-1 w-3 h-3 bg-slate-500 rounded-full"></div>
                <div className="absolute top-2 left-2 w-1 h-1 bg-slate-300 rounded-full"></div>
              </div>
              <div className={`absolute -bottom-3 left-38 w-5 h-5 bg-slate-700 rounded-full shadow-lg border-2 border-slate-600 ${isPlaying ? 'animate-spin' : ''}`}
                   style={{ animationDuration: '0.08s', animationTimingFunction: 'linear' }}>
                <div className="absolute top-1 left-1 w-3 h-3 bg-slate-500 rounded-full"></div>
                <div className="absolute top-2 left-2 w-1 h-1 bg-slate-300 rounded-full"></div>
              </div>

              {/* Enhanced wind displacement and aerodynamic effects */}
              {isPlaying && (
                <>
                  {/* Primary wind displacement */}
                  <div className="absolute -left-12 top-0 w-12 h-12">
                    {[...Array(6)].map((_, i) => (
                      <div
                        key={`wind-${i}`}
                        className="absolute w-8 h-0.5 bg-cyan-400 opacity-70 animate-pulse"
                        style={{
                          top: `${15 + i * 12}%`,
                          left: `${i * 2}px`,
                          animationDelay: `${i * 0.15}s`,
                          animationDuration: '0.8s'
                        }}
                      ></div>
                    ))}
                  </div>

                  {/* Trailing wind effects */}
                  <div className="absolute -left-20 top-2 w-16 h-8">
                    {[...Array(4)].map((_, i) => (
                      <div
                        key={`trail-${i}`}
                        className="absolute w-12 h-0.5 bg-gradient-to-r from-cyan-300 to-transparent opacity-50 animate-pulse"
                        style={{
                          top: `${20 + i * 15}%`,
                          animationDelay: `${i * 0.2}s`,
                          animationDuration: '1s'
                        }}
                      ></div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Wind Flow Lines */}
        {isPlaying && (
          <div className="absolute inset-0">
            {[...Array(8)].map((_, i) => (
              <div
                key={`wind-${i}`}
                className="absolute animate-pulse"
                style={{
                  left: `${10 + i * 10}%`,
                  top: `${40 + (i % 2) * 20}%`,
                  animationDelay: `${i * 0.2}s`
                }}
              >
                <div className="w-8 h-0.5 bg-cyan-400 opacity-60 animate-pulse"></div>
              </div>
            ))}
          </div>
        )}

        {/* Energy Flow Indicators */}
        {isPlaying && (
          <div className="absolute top-4 left-4 right-4">
            <div className="flex justify-between items-center">
              <div className="text-emerald-400 text-sm font-semibold">
                Energy Generation: {[...Array(8)].reduce((count, _, i) => count + getTurbineActivity(i), 0)} Active
              </div>
              <div className="flex space-x-2">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-2 h-2 rounded-full ${getTurbineActivity(i) ? 'bg-emerald-400 animate-pulse' : 'bg-slate-600'}`}
                    style={{ animationDelay: `${i * 0.1}s` }}
                  ></div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <button
            onClick={toggleAnimation}
            className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg font-semibold hover:bg-opacity-30 transition-all duration-300"
          >
            {isPlaying ? 'Pause' : 'Play'} Animation
          </button>
        </div>
      )}
    </div>
  );
}
