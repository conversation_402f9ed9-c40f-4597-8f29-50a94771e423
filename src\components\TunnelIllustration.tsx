'use client';

import { useState } from 'react';
import Image from 'next/image';

interface TunnelIllustrationProps {
  className?: string;
  showOverlay?: boolean;
  interactive?: boolean;
}

export default function TunnelIllustration({ 
  className = '', 
  showOverlay = true,
  interactive = false 
}: TunnelIllustrationProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Placeholder for when the professional illustration is ready
  const illustrationSrc = "/tunnel-wind-capture-illustration.jpg"; // Replace with actual image path

  return (
    <div className={`relative overflow-hidden rounded-2xl ${className}`}>
      {/* Professional Illustration Container */}
      <div className="relative aspect-video bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900">
        {/* Placeholder until professional illustration is ready */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <div className="w-24 h-24 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold mb-4">Professional Illustration</h3>
            <p className="text-slate-300 max-w-md mx-auto">
              High-quality technical illustration showing tunnel wind capture technology in action
            </p>
            <div className="mt-6 text-sm text-slate-400">
              Designed for investor presentations and corporate demonstrations
            </div>
          </div>
        </div>

        {/* Future: Professional illustration will replace placeholder */}
        {/* 
        <Image
          src={illustrationSrc}
          alt="Tunnel Wind Capture Technology Demonstration"
          fill
          className="object-cover"
          onLoad={() => setIsLoaded(true)}
          priority
        />
        */}

        {/* Interactive Overlay Information */}
        {showOverlay && (
          <div className="absolute inset-0">
            {/* Technical Specifications Overlay */}
            <div className="absolute top-4 left-4 bg-slate-900 bg-opacity-80 rounded-lg p-4 text-white max-w-xs">
              <h4 className="font-bold text-cyan-400 mb-2">Technical Specifications</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-300">Train Speed:</span>
                  <span>300 km/h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Turbines:</span>
                  <span>8 Units</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Power Output:</span>
                  <span className="text-emerald-400">120 kW</span>
                </div>
              </div>
            </div>

            {/* Wind Flow Indicator */}
            <div className="absolute top-4 right-4 bg-slate-900 bg-opacity-80 rounded-lg p-4 text-white">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-semibold">Wind Flow Active</span>
              </div>
            </div>

            {/* Energy Generation Status */}
            <div className="absolute bottom-4 left-4 bg-slate-900 bg-opacity-80 rounded-lg p-4 text-white">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-emerald-400 rounded-full animate-pulse"></div>
                <div>
                  <div className="text-sm font-semibold">Energy Generation</div>
                  <div className="text-xs text-slate-300">Continuous Operation</div>
                </div>
              </div>
            </div>

            {/* Interactive Hotspots (if interactive) */}
            {interactive && (
              <>
                {/* Turbine Hotspots */}
                <button
                  className="absolute top-1/2 left-1/4 w-6 h-6 bg-cyan-500 bg-opacity-80 rounded-full border-2 border-white animate-pulse hover:scale-110 transition-transform"
                  onClick={() => setShowDetails(!showDetails)}
                  aria-label="Turbine Details"
                />
                <button
                  className="absolute top-1/3 right-1/4 w-6 h-6 bg-cyan-500 bg-opacity-80 rounded-full border-2 border-white animate-pulse hover:scale-110 transition-transform"
                  onClick={() => setShowDetails(!showDetails)}
                  aria-label="Turbine Details"
                />

                {/* Train Hotspot */}
                <button
                  className="absolute bottom-1/3 left-1/2 w-6 h-6 bg-emerald-500 bg-opacity-80 rounded-full border-2 border-white animate-pulse hover:scale-110 transition-transform"
                  onClick={() => setShowDetails(!showDetails)}
                  aria-label="Train Details"
                />
              </>
            )}
          </div>
        )}

        {/* Detail Panel (for interactive mode) */}
        {interactive && showDetails && (
          <div className="absolute inset-0 bg-slate-900 bg-opacity-95 flex items-center justify-center">
            <div className="bg-white rounded-2xl p-8 max-w-md mx-4">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-2xl font-bold text-slate-800">Technology Details</h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-slate-500 hover:text-slate-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-slate-800 mb-2">Savonius Turbines</h4>
                  <p className="text-slate-600 text-sm">
                    Vertical-axis wind turbines optimized for tunnel environments, 
                    capturing wind energy from high-speed train passage.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold text-slate-800 mb-2">Wind Capture</h4>
                  <p className="text-slate-600 text-sm">
                    Trains traveling at 200-350 km/h create powerful, predictable 
                    wind currents that are efficiently captured by our turbine system.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold text-slate-800 mb-2">Energy Output</h4>
                  <p className="text-slate-600 text-sm">
                    Each turbine generates 5-15 kW of clean energy, with multiple 
                    units providing substantial power for rail infrastructure.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Professional Caption */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900 to-transparent p-6">
        <div className="text-white">
          <h3 className="text-lg font-bold mb-2">Tunnel Wind Capture Technology</h3>
          <p className="text-slate-300 text-sm">
            Professional demonstration of high-speed rail wind energy harvesting system
          </p>
        </div>
      </div>
    </div>
  );
}
