'use client';

import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface PerformanceDashboardProps {
  className?: string;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({ className = '' }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [energyData, setEnergyData] = useState<number[]>([]);
  const [trainPassages, setTrainPassages] = useState(0);
  const [totalEnergy, setTotalEnergy] = useState(0);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
      
      // Simulate energy generation (0-25 kW based on train activity)
      const newEnergyValue = Math.random() * 25;
      setEnergyData(prev => {
        const newData = [...prev, newEnergyValue];
        return newData.slice(-20); // Keep last 20 data points
      });
      
      // Update totals
      setTotalEnergy(prev => prev + newEnergyValue / 60); // Convert to kWh
      
      // Randomly increment train passages
      if (Math.random() > 0.7) {
        setTrainPassages(prev => prev + 1);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Chart configurations with industrial theme
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#e2e8f0',
          font: {
            family: 'Inter, sans-serif',
            size: 12,
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
      y: {
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11,
          },
        },
        grid: {
          color: '#334155',
        },
      },
    },
  };

  // Real-time energy generation chart
  const energyChartData = {
    labels: energyData.map((_, index) => `${index * 3}s`),
    datasets: [
      {
        label: 'Energy Output (kW)',
        data: energyData,
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
      },
    ],
  };

  // Environmental impact data
  const co2Reduced = (totalEnergy * 0.5).toFixed(1); // kg CO2
  const homesPowered = Math.floor(totalEnergy / 10); // Approximate homes powered

  // Efficiency doughnut chart
  const efficiencyData = {
    labels: ['Active Turbines', 'Standby Turbines'],
    datasets: [
      {
        data: [6, 2],
        backgroundColor: ['#10b981', '#64748b'],
        borderColor: ['#059669', '#475569'],
        borderWidth: 2,
      },
    ],
  };

  return (
    <div className={`dashboard-card p-6 lg:p-8 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 gap-4">
        <div>
          <h3 className="text-2xl lg:text-3xl font-bold text-white mb-2">Live Performance Dashboard</h3>
          <p className="text-slate-300 text-sm lg:text-base">Real-time monitoring of tunnel wind capture systems</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
          <div className="text-sm text-slate-300">
            Last Updated: {currentTime.toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
        <div className="dashboard-metric p-4 lg:p-6 group hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="text-cyan-400 text-sm font-semibold">Current Output</div>
            <div className="w-8 h-8 bg-cyan-400/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-cyan-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="text-2xl lg:text-3xl font-bold text-white">
            {energyData[energyData.length - 1]?.toFixed(1) || '0.0'} kW
          </div>
          <div className="text-xs text-slate-400 mt-1">Real-time generation</div>
        </div>

        <div className="dashboard-metric p-4 lg:p-6 group hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="text-emerald-400 text-sm font-semibold">Total Generated</div>
            <div className="w-8 h-8 bg-emerald-400/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="text-2xl lg:text-3xl font-bold text-white">{totalEnergy.toFixed(1)} kWh</div>
          <div className="text-xs text-slate-400 mt-1">Cumulative energy</div>
        </div>

        <div className="dashboard-metric p-4 lg:p-6 group hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="text-blue-400 text-sm font-semibold">Train Passages</div>
            <div className="w-8 h-8 bg-blue-400/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="text-2xl lg:text-3xl font-bold text-white">{trainPassages}</div>
          <div className="text-xs text-slate-400 mt-1">Today&apos;s count</div>
        </div>

        <div className="dashboard-metric p-4 lg:p-6 group hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="text-purple-400 text-sm font-semibold">System Efficiency</div>
            <div className="w-8 h-8 bg-purple-400/20 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="text-2xl lg:text-3xl font-bold text-white">87%</div>
          <div className="text-xs text-slate-400 mt-1">Operational status</div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Energy Chart */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">Real-time Energy Generation</h4>
          <div className="h-64">
            <Line data={energyChartData} options={chartOptions} />
          </div>
        </div>

        {/* Turbine Status */}
        <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
          <h4 className="text-lg font-semibold text-white mb-4">Turbine Status</h4>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut 
                data={efficiencyData} 
                options={{
                  ...chartOptions,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: '#e2e8f0',
                        font: {
                          family: 'Inter, sans-serif',
                          size: 12,
                        },
                      },
                    },
                  },
                }} 
              />
            </div>
          </div>
        </div>
      </div>

      {/* Environmental Impact */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-lg p-4 text-white">
          <div className="text-emerald-100 text-sm font-semibold">CO₂ Reduced</div>
          <div className="text-2xl font-bold">{co2Reduced} kg</div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-4 text-white">
          <div className="text-blue-100 text-sm font-semibold">Homes Powered</div>
          <div className="text-2xl font-bold">{homesPowered}</div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-4 text-white">
          <div className="text-purple-100 text-sm font-semibold">Uptime</div>
          <div className="text-2xl font-bold">99.2%</div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
