'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface FlappyTrainProps {
  className?: string;
}

interface Obstacle {
  id: number;
  x: number;
  gapY: number;
  gapSize: number;
  passed: boolean;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
}

interface GameState {
  isPlaying: boolean;
  isGameOver: boolean;
  score: number;
  trainY: number;
  trainVelocity: number;
  obstacles: Obstacle[];
  particles: Particle[];
  lastScoreTime: number;
}

const GAME_CONFIG = {
  trainSize: 40,
  gravity: 0.5,
  jumpStrength: -8,
  obstacleWidth: 60,
  obstacleSpeed: 2.5,
  gapSize: 180,
  spawnDistance: 280,
  gameHeight: 400,
  gameWidth: 600,
  maxVelocity: 15,
  minVelocity: -15,
};

export default function FlappyTrain({ className = '' }: FlappyTrainProps) {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>(0);
  const particleIdCounter = useRef<number>(0);
  
  const [gameState, setGameState] = useState<GameState>({
    isPlaying: false,
    isGameOver: false,
    score: 0,
    trainY: GAME_CONFIG.gameHeight / 2,
    trainVelocity: 0,
    obstacles: [],
    particles: [],
    lastScoreTime: 0,
  });

  // Generate new obstacle
  const createObstacle = useCallback((x: number, id: number): Obstacle => {
    const gapY = Math.random() * (GAME_CONFIG.gameHeight - GAME_CONFIG.gapSize - 100) + 50;
    return {
      id,
      x,
      gapY,
      gapSize: GAME_CONFIG.gapSize,
      passed: false,
    };
  }, []);

  // Create particles with unique IDs
  const createParticles = useCallback((x: number, y: number, color: string, count: number = 5): Particle[] => {
    return Array.from({ length: count }, () => {
      particleIdCounter.current += 1;
      return {
        id: particleIdCounter.current, // Use incrementing counter for unique IDs
        x,
        y,
        vx: (Math.random() - 0.5) * 8,
        vy: (Math.random() - 0.5) * 8,
        life: 30,
        maxLife: 30,
        color,
      };
    });
  }, []);

  // Initialize game
  const initGame = useCallback(() => {
    const initialObstacles = [
      createObstacle(GAME_CONFIG.gameWidth, 1),
      createObstacle(GAME_CONFIG.gameWidth + GAME_CONFIG.spawnDistance, 2),
    ];

    setGameState({
      isPlaying: false,
      isGameOver: false,
      score: 0,
      trainY: GAME_CONFIG.gameHeight / 2,
      trainVelocity: -8,
      obstacles: initialObstacles,
      particles: [],
      lastScoreTime: 0,
    });
  }, [createObstacle]);

  // Start game
  const startGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      isPlaying: true,
      isGameOver: false,
    }));
  }, []);

  // Jump function
  const jump = useCallback(() => {
    if (!gameState.isPlaying || gameState.isGameOver) return;

    setGameState(prev => {
      // Create jump particles with color based on speed level
      const speedLevel = Math.floor(prev.score / 3); // Change color every 3 points
      const particleColors = ['#22d3ee', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
      const particleColor = particleColors[Math.min(speedLevel, particleColors.length - 1)];

      const jumpParticles = createParticles(
        120, // train x position
        prev.trainY + GAME_CONFIG.trainSize / 2, // train center
        particleColor,
        Math.min(10, 6 + speedLevel) // More particles at higher speeds
      );

      return {
        ...prev,
        trainVelocity: GAME_CONFIG.jumpStrength,
        particles: [...prev.particles, ...jumpParticles],
      };
    });
  }, [gameState.isPlaying, gameState.isGameOver, createParticles]);

  // Collision detection
  const checkCollisions = useCallback((trainY: number, obstacles: Obstacle[]): boolean => {
    const trainLeft = 100;
    const trainRight = trainLeft + GAME_CONFIG.trainSize;
    const trainTop = trainY;
    const trainBottom = trainY + GAME_CONFIG.trainSize;

    // Check tunnel walls
    if (trainTop <= 0 || trainBottom >= GAME_CONFIG.gameHeight) {
      return true;
    }

    // Check obstacles
    for (const obstacle of obstacles) {
      const obstacleLeft = obstacle.x;
      const obstacleRight = obstacle.x + GAME_CONFIG.obstacleWidth;

      if (trainRight > obstacleLeft && trainLeft < obstacleRight) {
        // Check if train is in the gap
        if (trainTop < obstacle.gapY || trainBottom > obstacle.gapY + obstacle.gapSize) {
          return true;
        }
      }
    }

    return false;
  }, []);

  // Game loop
  useEffect(() => {
    if (!gameState.isPlaying || gameState.isGameOver) return;

    const gameLoop = () => {
      setGameState(prev => {
        let newTrainY = prev.trainY + prev.trainVelocity;
        let newTrainVelocity = prev.trainVelocity + GAME_CONFIG.gravity;
        let newObstacles = [...prev.obstacles];
        let newScore = prev.score;
        let obstacleIdCounter = Math.max(...prev.obstacles.map(o => o.id)) + 1;

        // Clamp train velocity
        newTrainVelocity = Math.max(GAME_CONFIG.minVelocity, Math.min(GAME_CONFIG.maxVelocity, newTrainVelocity));

        // Calculate progressive speed increase based on score
        const speedMultiplier = 1 + (prev.score * 0.1); // Increase speed by 10% per point
        const currentObstacleSpeed = GAME_CONFIG.obstacleSpeed * Math.min(speedMultiplier, 2.5); // Cap at 2.5x speed

        // Move obstacles with progressive speed
        newObstacles = newObstacles.map(obstacle => ({
          ...obstacle,
          x: obstacle.x - currentObstacleSpeed,
        }));

        // Remove off-screen obstacles and add new ones
        newObstacles = newObstacles.filter(obstacle => obstacle.x > -GAME_CONFIG.obstacleWidth);

        // Add new obstacles
        const lastObstacle = newObstacles[newObstacles.length - 1];
        if (lastObstacle && lastObstacle.x < GAME_CONFIG.gameWidth - GAME_CONFIG.spawnDistance) {
          newObstacles.push(createObstacle(GAME_CONFIG.gameWidth, obstacleIdCounter));
        }

        // Check for scoring and create particles
        let newParticles = [...prev.particles];
        newObstacles = newObstacles.map(obstacle => {
          if (!obstacle.passed && obstacle.x + GAME_CONFIG.obstacleWidth < 100) {
            newScore++;
            // Create score particles
            const scoreParticles = createParticles(
              120,
              obstacle.gapY + obstacle.gapSize / 2,
              '#06b6d4',
              8
            );
            newParticles = [...newParticles, ...scoreParticles];
            return { ...obstacle, passed: true };
          }
          return obstacle;
        });

        // Update particles
        newParticles = newParticles
          .map(particle => ({
            ...particle,
            x: particle.x + particle.vx,
            y: particle.y + particle.vy,
            vy: particle.vy + 0.2, // gravity on particles
            life: particle.life - 1,
          }))
          .filter(particle => particle.life > 0);

        // Check collisions
        const hasCollision = checkCollisions(newTrainY, newObstacles);

        if (hasCollision) {
          return {
            ...prev,
            isGameOver: true,
            isPlaying: false,
          };
        }

        return {
          ...prev,
          trainY: newTrainY,
          trainVelocity: newTrainVelocity,
          obstacles: newObstacles,
          particles: newParticles,
          score: newScore,
          lastScoreTime: newScore > prev.score ? Date.now() : prev.lastScoreTime,
        };
      });

      animationRef.current = requestAnimationFrame(gameLoop);
    };

    animationRef.current = requestAnimationFrame(gameLoop);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameState.isPlaying, gameState.isGameOver, checkCollisions, createObstacle]);

  // Handle click/tap and keyboard
  useEffect(() => {
    const handleClick = () => {
      if (!gameState.isPlaying && !gameState.isGameOver) {
        startGame();
      } else if (gameState.isPlaying) {
        jump();
      } else if (gameState.isGameOver) {
        initGame();
      }
    };

    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.code === 'Space' || event.code === 'ArrowUp' || event.code === 'KeyW') {
        event.preventDefault();
        handleClick();
      }
    };

    const gameElement = gameRef.current;
    if (gameElement) {
      gameElement.addEventListener('click', handleClick);
      document.addEventListener('keydown', handleKeyPress);
      return () => {
        gameElement.removeEventListener('click', handleClick);
        document.removeEventListener('keydown', handleKeyPress);
      };
    }
  }, [gameState.isPlaying, gameState.isGameOver, startGame, jump, initGame]);

  // Initialize game on mount
  useEffect(() => {
    initGame();
  }, [initGame]);

  return (
    <div className={`relative bg-slate-900 rounded-xl overflow-hidden ${className}`}>
      {/* Game Area */}
      <div
        ref={gameRef}
        className="relative bg-gradient-to-br from-slate-800 to-slate-900 cursor-pointer select-none mx-auto"
        style={{
          width: GAME_CONFIG.gameWidth,
          height: GAME_CONFIG.gameHeight,
          maxWidth: '100%'
        }}
      >
        {/* Tunnel Background */}
        <div className="absolute inset-0">
          {/* Tunnel walls */}
          <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-b from-slate-600 to-slate-700"></div>
          <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-t from-slate-600 to-slate-700"></div>
          
          {/* Tunnel depth lines */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-slate-600 opacity-20"
              style={{
                left: `${i * 5}%`,
                top: '10px',
                bottom: '10px',
                width: '1px',
              }}
            />
          ))}
        </div>

        {/* Train */}
        <div
          className="absolute transition-none"
          style={{
            left: '100px',
            top: `${gameState.trainY}px`,
            width: `${GAME_CONFIG.trainSize}px`,
            height: `${GAME_CONFIG.trainSize}px`,
            transform: `rotate(${Math.max(-30, Math.min(30, gameState.trainVelocity * 2))}deg)`,
            zIndex: 10,
          }}
        >
          {/* Motion blur effects */}
          {gameState.isPlaying && (
            <>
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-300 to-transparent opacity-30 blur-sm"
                style={{ transform: `translateX(-${Math.abs(gameState.trainVelocity) * 2}px)` }}
              ></div>
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-20 blur-md"
                style={{ transform: `translateX(-${Math.abs(gameState.trainVelocity) * 3}px)` }}
              ></div>
            </>
          )}

          {/* Train body - simplified version of the original */}
          <div className="w-full h-full bg-gradient-to-r from-slate-100 via-slate-200 to-slate-300 rounded-lg shadow-lg relative border border-slate-300">
            {/* Cyan accent stripe */}
            <div className="absolute top-1 left-1 right-1 h-1 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full"></div>

            {/* Train nose */}
            <div className="absolute -right-2 top-1/2 w-4 h-6 bg-gradient-to-r from-slate-300 to-slate-400 rounded-r-full transform -translate-y-1/2">
              <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-400 rounded-full"></div>
              <div className="absolute bottom-1 right-1 w-1 h-1 bg-red-500 rounded-full"></div>
            </div>

            {/* Wind trail effects */}
            {gameState.isPlaying && (
              <div className="absolute -left-8 top-1/2 transform -translate-y-1/2">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-6 h-0.5 bg-cyan-400 opacity-60 animate-pulse"
                    style={{
                      left: `${i * -4}px`,
                      top: `${(i - 1) * 4}px`,
                      animationDelay: `${i * 0.1}s`,
                      animationDuration: '0.5s'
                    }}
                  ></div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Particles */}
        {gameState.particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${particle.x}px`,
              top: `${particle.y}px`,
              backgroundColor: particle.color,
              opacity: particle.life / particle.maxLife,
              transform: `scale(${particle.life / particle.maxLife})`,
            }}
          />
        ))}

        {/* Wind Flow Effects */}
        {gameState.isPlaying && (
          <div className="absolute inset-0">
            {[...Array(Math.min(8 + gameState.score, 16))].map((_, i) => {
              const speedMultiplier = 1 + (gameState.score * 0.1);
              const animationSpeed = Math.max(0.3, 1 - (speedMultiplier * 0.1));

              return (
                <div
                  key={`wind-${i}`}
                  className="absolute animate-pulse"
                  style={{
                    left: `${(i * 12) % 100}%`,
                    top: `${20 + (i % 3) * 30}%`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: `${animationSpeed}s`
                  }}
                >
                  <div
                    className="h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"
                    style={{
                      width: `${Math.min(16, 4 + speedMultiplier * 2)}px`,
                      opacity: Math.min(0.8, 0.4 + speedMultiplier * 0.1)
                    }}
                  ></div>
                </div>
              );
            })}
          </div>
        )}

        {/* Obstacles (Turbines) */}
        {gameState.obstacles.map((obstacle) => (
          <div key={obstacle.id}>
            {/* Top turbine */}
            <div
              className="absolute"
              style={{
                left: `${obstacle.x}px`,
                top: '0px',
                width: `${GAME_CONFIG.obstacleWidth}px`,
                height: `${obstacle.gapY}px`,
              }}
            >
              <div className="w-full h-full bg-gradient-to-b from-slate-400 to-slate-500 relative">
                {/* Turbine housing at bottom */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-12 bg-slate-400 rounded-lg">
                  {/* Spinning turbine blades */}
                  <div className="absolute inset-0 animate-spin" style={{ animationDuration: '0.5s' }}>
                    <div className="absolute top-0 left-0 w-4 h-6 border-2 border-cyan-500 border-b-0 rounded-t-full bg-gradient-to-b from-cyan-400 to-transparent opacity-90"></div>
                    <div className="absolute bottom-0 right-0 w-4 h-6 border-2 border-cyan-500 border-t-0 rounded-b-full bg-gradient-to-t from-cyan-400 to-transparent opacity-90"></div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Bottom turbine */}
            <div
              className="absolute"
              style={{
                left: `${obstacle.x}px`,
                top: `${obstacle.gapY + obstacle.gapSize}px`,
                width: `${GAME_CONFIG.obstacleWidth}px`,
                height: `${GAME_CONFIG.gameHeight - obstacle.gapY - obstacle.gapSize}px`,
              }}
            >
              <div className="w-full h-full bg-gradient-to-t from-slate-400 to-slate-500 relative">
                {/* Turbine housing at top */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-12 bg-slate-400 rounded-lg">
                  {/* Spinning turbine blades */}
                  <div className="absolute inset-0 animate-spin" style={{ animationDuration: '0.5s' }}>
                    <div className="absolute top-0 left-0 w-4 h-6 border-2 border-cyan-500 border-b-0 rounded-t-full bg-gradient-to-b from-cyan-400 to-transparent opacity-90"></div>
                    <div className="absolute bottom-0 right-0 w-4 h-6 border-2 border-cyan-500 border-t-0 rounded-b-full bg-gradient-to-t from-cyan-400 to-transparent opacity-90"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Score Display */}
        <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white border border-slate-600">
          <div
            className={`text-2xl font-bold text-cyan-400 transition-all duration-200 ${
              Date.now() - gameState.lastScoreTime < 500 ? 'scale-125 text-emerald-400' : ''
            }`}
          >
            {gameState.score}
          </div>
          <div className="text-xs text-slate-300">Turbines Passed</div>
          {Date.now() - gameState.lastScoreTime < 500 && (
            <div className="absolute -top-2 -right-2 text-emerald-400 text-sm font-bold animate-bounce">
              +1
            </div>
          )}
        </div>

        {/* Speed Indicator */}
        {gameState.isPlaying && (
          <div className="absolute top-4 right-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white border border-slate-600">
            <div className="text-sm font-semibold text-emerald-400">
              Game Speed: {Math.round((1 + (gameState.score * 0.1)) * 100)}%
            </div>
            <div className="text-xs text-slate-300">
              Train: {Math.abs(Math.round(gameState.trainVelocity * 10))} km/h
            </div>
          </div>
        )}

        {/* Game States Overlay */}
        {!gameState.isPlaying && !gameState.isGameOver && (
          <div className="absolute inset-0 bg-slate-900 bg-opacity-90 flex items-center justify-center">
            <div className="text-center text-white bg-slate-800 bg-opacity-90 rounded-xl p-8 border border-slate-600">
              <h2 className="text-4xl font-bold text-cyan-400 mb-4">Flappy Train</h2>
              <p className="text-slate-300 mb-6 max-w-sm">Navigate your high-speed train through the spinning Savonius turbine obstacles!</p>
              <div className="text-sm text-slate-400 mb-4">Click anywhere or use the button below to start</div>
              <div className="flex items-center justify-center space-x-4 text-xs text-slate-500">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-slate-300 rounded"></div>
                  <span>Train</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-cyan-400 rounded animate-spin"></div>
                  <span>Turbines</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {gameState.isGameOver && (
          <div className="absolute inset-0 bg-slate-900 bg-opacity-90 flex items-center justify-center">
            <div className="text-center text-white bg-slate-800 bg-opacity-90 rounded-xl p-8 border border-slate-600">
              <h2 className="text-4xl font-bold text-red-400 mb-4">Game Over</h2>
              <div className="mb-6">
                <p className="text-2xl text-cyan-400 mb-2">Final Score: {gameState.score}</p>
                <p className="text-slate-300">Turbines successfully passed: {gameState.score}</p>
              </div>
              <div className="text-sm text-slate-400 mb-4">Click anywhere or use the button below to restart</div>
              <div className="text-xs text-slate-500">
                {gameState.score === 0 ? "Try clicking to make the train jump!" :
                 gameState.score < 5 ? "Good start! Keep practicing!" :
                 gameState.score < 10 ? "Great job! You're getting the hang of it!" :
                 "Excellent piloting skills!"}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Play Button */}
      <div className="bg-slate-800 p-4 text-center border-t border-slate-700">
        <button
          onClick={() => {
            if (!gameState.isPlaying && !gameState.isGameOver) {
              startGame();
            } else if (gameState.isGameOver) {
              initGame();
            }
          }}
          disabled={gameState.isPlaying}
          className={`
            px-8 py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg border
            ${gameState.isPlaying
              ? 'bg-slate-600 text-slate-400 border-slate-600 cursor-not-allowed'
              : 'bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-cyan-400 hover:from-cyan-600 hover:to-cyan-700 hover:border-cyan-300 hover:shadow-cyan-500/25 hover:shadow-xl active:scale-95'
            }
          `}
        >
          {!gameState.isPlaying && !gameState.isGameOver ? '🚄 Start Game' :
           gameState.isGameOver ? '🔄 Play Again' : '🎮 Playing...'}
        </button>

        {/* Game Instructions */}
        <div className="mt-3 text-xs text-slate-400 max-w-md mx-auto">
          <p className="mb-1">
            <span className="text-cyan-400">Click</span>, <span className="text-cyan-400">tap</span>, or press <span className="text-cyan-400">Space/↑/W</span> to make the train jump
          </p>
          <p className="mb-1">Navigate through the gaps between spinning turbines to score points!</p>
          <p className="text-emerald-400">⚡ Game speed increases with each point scored!</p>
        </div>
      </div>
    </div>
  );
}
