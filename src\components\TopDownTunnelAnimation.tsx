'use client';

import { useState, useRef, useEffect } from 'react';

interface TopDownTunnelAnimationProps {
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  showMetrics?: boolean;
}

export default function TopDownTunnelAnimation({ 
  className = '', 
  autoPlay = true,
  showControls = true,
  showMetrics = true
}: TopDownTunnelAnimationProps) {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentTime, setCurrentTime] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Animation state for demonstration
  const [trainPosition, setTrainPosition] = useState({ x: -400, y: -225, scale: 0.3 });
  const [turbineStates, setTurbineStates] = useState(Array(8).fill({ spinning: false, power: 0 }));

  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = (prev + 0.1) % 8; // 8-second cycle
          updateAnimationState(newTime);
          return newTime;
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isPlaying]);

  const updateAnimationState = (time: number) => {
    // Calculate train position along diagonal path
    const progress = time / 8;
    const x = -400 + (800 * progress);
    const y = -225 + (450 * progress);
    const scale = 0.3 + (0.9 * Math.sin(progress * Math.PI));
    
    setTrainPosition({ x, y, scale });

    // Update turbine states based on train proximity
    const newTurbineStates = Array(8).fill(null).map((_, index) => {
      const turbineX = -300 + (index * 100);
      const distance = Math.abs(x - turbineX);
      const isActive = distance < 150;
      const power = isActive ? Math.max(0, 15 - (distance / 10)) : 0;
      
      return {
        spinning: isActive,
        power: Math.round(power * 10) / 10
      };
    });
    
    setTurbineStates(newTurbineStates);
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  const resetAnimation = () => {
    setCurrentTime(0);
    setTrainPosition({ x: -400, y: -225, scale: 0.3 });
    setTurbineStates(Array(8).fill({ spinning: false, power: 0 }));
  };

  return (
    <div className={`relative bg-slate-900 rounded-2xl overflow-hidden ${className}`}>
      {/* Animation Container */}
      <div className="relative aspect-video bg-gradient-to-br from-slate-800 to-slate-900">
        
        {/* Placeholder for actual animation video/canvas */}
        <div className="absolute inset-0 flex items-center justify-center">
          {/* This will be replaced with the actual top-down animation */}
          <canvas
            ref={canvasRef}
            className="w-full h-full"
            style={{ imageRendering: 'crisp-edges' }}
          />
          
          {/* Placeholder content until animation is ready */}
          <div className="absolute inset-0 flex items-center justify-center text-white">
            <div className="text-center p-8">
              <div className="w-32 h-32 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                <svg className="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4">Top-Down Animation</h3>
              <p className="text-slate-300 max-w-md mx-auto mb-4">
                Professional top-down view of tunnel wind capture technology
              </p>
              <div className="text-sm text-slate-400">
                Train trajectory: Diagonal approach with perspective scaling
              </div>
            </div>
          </div>
        </div>

        {/* Professional Overlays */}
        {showMetrics && (
          <>
            {/* Animation Progress */}
            <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm">
              <div className="font-semibold mb-1">Animation Progress</div>
              <div className="flex items-center space-x-2">
                <div className="w-24 h-2 bg-slate-600 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-cyan-400 transition-all duration-100"
                    style={{ width: `${(currentTime / 8) * 100}%` }}
                  ></div>
                </div>
                <span className="text-xs text-slate-300">
                  {Math.round((currentTime / 8) * 100)}%
                </span>
              </div>
            </div>

            {/* Train Status */}
            <div className="absolute top-4 right-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm">
              <div className="font-semibold mb-2">Train Status</div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-slate-300">Speed:</span>
                  <span>300 km/h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Scale:</span>
                  <span>{trainPosition.scale.toFixed(1)}x</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Status:</span>
                  <span className={isPlaying ? "text-emerald-400" : "text-amber-400"}>
                    {isPlaying ? "Moving" : "Stopped"}
                  </span>
                </div>
              </div>
            </div>

            {/* Turbine Status Panel */}
            <div className="absolute bottom-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm max-w-xs">
              <div className="font-semibold mb-2">Turbine Status</div>
              <div className="grid grid-cols-4 gap-2">
                {turbineStates.map((turbine, index) => (
                  <div key={index} className="text-center">
                    <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${
                      turbine.spinning ? 'bg-emerald-400 animate-pulse' : 'bg-slate-500'
                    }`}></div>
                    <div className="text-xs text-slate-300">
                      {turbine.power.toFixed(1)}kW
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-2 pt-2 border-t border-slate-600">
                <div className="flex justify-between">
                  <span className="text-slate-300">Total Output:</span>
                  <span className="text-emerald-400">
                    {turbineStates.reduce((sum, t) => sum + t.power, 0).toFixed(1)}kW
                  </span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Animation Controls */}
        {showControls && (
          <div className="absolute bottom-4 right-4 flex space-x-2">
            <button
              onClick={togglePlayback}
              className="bg-slate-800 bg-opacity-90 text-white p-3 rounded-lg hover:bg-opacity-100 transition-all duration-200"
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
            
            <button
              onClick={resetAnimation}
              className="bg-slate-800 bg-opacity-90 text-white p-3 rounded-lg hover:bg-opacity-100 transition-all duration-200"
              aria-label="Reset"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        )}

        {/* Professional Branding */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-slate-800 bg-opacity-90 rounded-lg px-4 py-2 text-white text-sm">
            <div className="font-semibold">Tunnel Wind Capture Technology</div>
            <div className="text-xs text-slate-300">Top-Down Perspective View</div>
          </div>
        </div>
      </div>

      {/* Technical Information Panel */}
      <div className="bg-slate-800 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-cyan-400 font-semibold mb-1">Perspective View</div>
            <div className="text-slate-300">Top-down diagonal trajectory</div>
          </div>
          <div className="text-center">
            <div className="text-emerald-400 font-semibold mb-1">Scale Effect</div>
            <div className="text-slate-300">0.3x to 1.2x progression</div>
          </div>
          <div className="text-center">
            <div className="text-purple-400 font-semibold mb-1">Animation</div>
            <div className="text-slate-300">8-second cycle, 60fps</div>
          </div>
        </div>
      </div>
    </div>
  );
}
