'use client';

import React, { useState, useEffect } from 'react';

interface SectionNavigatorProps {
  sections: Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
  }>;
  className?: string;
}

const SectionNavigator: React.FC<SectionNavigatorProps> = ({ sections, className = '' }) => {
  const [activeSection, setActiveSection] = useState<string>('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show navigator after scrolling past hero section
      setIsVisible(window.scrollY > 400);

      // Find active section
      const sectionElements = sections.map(section => 
        document.getElementById(section.id)
      ).filter(Boolean);

      const currentSection = sectionElements.find(element => {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return rect.top <= 100 && rect.bottom >= 100;
      });

      if (currentSection) {
        setActiveSection(currentSection.id);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [sections]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 100; // Account for fixed header
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed right-6 top-1/2 transform -translate-y-1/2 z-40 ${className}`}>
      <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200 p-2">
        <div className="space-y-2">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => scrollToSection(section.id)}
              className={`group relative w-12 h-12 rounded-xl transition-all duration-300 flex items-center justify-center ${
                activeSection === section.id
                  ? 'bg-gradient-to-br from-cyan-500 to-cyan-600 text-white shadow-lg scale-110'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-cyan-600'
              }`}
              title={section.label}
            >
              <div className="w-5 h-5">
                {section.icon}
              </div>
              
              {/* Tooltip */}
              <div className="absolute right-full mr-3 px-3 py-2 bg-slate-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none">
                {section.label}
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-slate-800"></div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SectionNavigator;
