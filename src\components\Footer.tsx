import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-xl font-bold">Aerotransit</span>
            </div>
            <p className="text-gray-300 text-sm">
              Revolutionizing sustainable energy through innovative wind power generation 
              technology that harnesses the power of high-speed rail transportation.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Idea Introduction
                </Link>
              </li>
              <li>
                <a href="/product" className="text-gray-300 hover:text-green-400 transition-colors duration-200">
                  Product Introduction
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Innovation Focus</h3>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li>• Sustainable Energy Solutions</li>
              <li>• Wind Power Technology</li>
              <li>• Railway Integration</li>
              <li>• Environmental Impact</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Aerotransit Generator Project. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Sustainable • Innovative • Efficient</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
